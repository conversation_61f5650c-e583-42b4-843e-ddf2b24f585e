import { CommonModule } from '@angular/common';
import { Component, EventEmitter, Input, Output } from '@angular/core';
import { HeicConversionService } from '@src/app/core/services/heic.service';
import { BrowserService } from '@src/app/modules/core/service/browser.service';
import { DynamicHeaderComponent } from '@src/app/shared/components/dynamic-header/dynamic-header.component';
import { SvgIconsComponent } from '@src/app/shared/components/svg-icons/svg-icons.component';
import { ProfileModel } from '@src/app/shared/models/profile.model';
import { AlertHandlerService } from 'src/app/modules/core/alerts/alert-handler.service';
import { TranslationService } from 'src/app/modules/core/service/translation.service';
import { LazyloadDirective } from 'src/app/shared/directives/lazyload.directive';
import { AuthService } from 'src/app/shared/services/auth.service';
import { ProfileService } from 'src/app/shared/services/profile.service';
import { FormUtils } from 'src/utils/form-utils';
import { AdsAndAddOnsModule } from "@src/app/modules/ads-and-add-ons/ads-and-add-ons.module";
import { Router } from '@angular/router';

@Component({
  selector: 'app-profile-cover',
  standalone: true,
  templateUrl: './profile-cover.component.html',
  styleUrls: ['./profile-cover.component.scss'],
  imports: [CommonModule, LazyloadDirective, SvgIconsComponent, DynamicHeaderComponent, AdsAndAddOnsModule]
})
export class ProfileCoverComponent {
  @Output() onEdit = new EventEmitter();
  @Input() user: ProfileModel;
  @Input() is: boolean;
  sellerImage!: string;
  imageUrl: string;
  fileToUpload: any;
  ratingModelVisible: boolean = false;
  loading: boolean = false;
  maxSize = 5 * 1024 * 1024;
  maxWidth = 1440;
  randomVersion = Math.floor(Math.random() * 10000);

  constructor(
    private _profileService: ProfileService,
    private _authService: AuthService,
    private alertService: AlertHandlerService,
    private translateService: TranslationService,
    private browser: BrowserService,
    private hcs: HeicConversionService,
    private router: Router,

  ) { }

  saveImage() {
    if (this.fileToUpload) {
      this.loading = true;
      let uploadData = new FormData();

      uploadData.append(
        'userprofile',
        this.fileToUpload.blob,
        this.fileToUpload.name
      );
      uploadData.append('id', this.user.userID!.toString());
      this._profileService
        .uploadCoverImgs(uploadData).subscribe(y => {
          let user = { ...this._authService.userValue };
          // user.coverURL = y?.body.imageURL;
          // this.user = { ...this.user, coverURL: user.coverURL };
          this.loading = false;
          this._authService.sendUserChanged(user);
          this.onEdit.emit(true);
          this.randomVersion = Math.floor(Math.random() * 10000);

        });
    }
  }
  goToEditProfile() {
    this.router.navigate(['/authentication/new-profile/edit-profile']);
  } 



  async handleFileInput($event: any) {
    if (this.browser.isBrowser()) {

      let file = $event.target.files;

      const fileImg = file[0];

      let convertedFile;

      if (!FormUtils.validImageFile(fileImg)) {
        this.alertService.warn({ message: this.translateService.instant('Invalid_Extension') });
        return;
      }

      var validHic = FormUtils.validHicFile(fileImg);

      if (validHic) {
        convertedFile = await this.hcs.convertIfHeic(fileImg);
      }

      this.resizeAndConvertImage(validHic ? convertedFile : fileImg, (event, blob) => {

        this.fileToUpload = {
          name: validHic ? convertedFile.name : fileImg.name,
          blob
        };
        this.user.imageURL = event.target.result;
        this.saveImage();
      });

    }


  }

  resizeAndConvertImage(file: File, callback: (e: any, blob: Blob) => void) {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = (event: any) => {
      const img = new Image();
      img.src = event.target.result;
      img.onload = () => {
        const canvas = document.createElement('canvas');
        const scaleSize = this.maxWidth / img.width;
        canvas.width = this.maxWidth;
        canvas.height = img.height * scaleSize;

        const ctx = canvas.getContext('2d');
        ctx!.drawImage(img, 0, 0, canvas.width, canvas.height);

        ((ee) => {
          canvas.toBlob(blob => {
            callback(ee, blob!);
          }, 'image/jpeg');
        })(event)
      };
    };
  }
}
