# Multi-stage Docker build for Storybook
FROM --platform=$BUILDPLATFORM node:22.12.0-alpine AS build

WORKDIR /app

# Install Python for node-gyp and other dependencies
RUN apk add --no-cache python3 make g++

# Copy package files
COPY package*.json ./
RUN npm install --legacy-peer-deps

# Copy source code
COPY . .

# Build Storybook
RUN npm run build-storybook

# Production stage with Nginx
FROM nginx:alpine AS production

COPY nginx-storybook.conf /etc/nginx/conf.d/default.conf


# Copy built Storybook to Nginx
COPY --from=build /app/storybook-static /usr/share/nginx/html


EXPOSE 6006
