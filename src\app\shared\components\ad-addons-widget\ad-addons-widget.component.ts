import { Ng<PERSON><PERSON>, NgIf } from '@angular/common';
import { Component, EventEmitter, Input, Output } from '@angular/core';
import { SvgIconsComponent } from '../svg-icons/svg-icons.component';
import { SharedBtnComponent } from '../shared-btn/shared-btn.component';
import { AdsAndAddOns } from '@src/app/modules/ads-and-add-ons/models/ads-and-add-ons.model';

@Component({
  selector: 'app-ad-addons-widget',
  standalone: true,
  imports: [NgIf, SvgIconsComponent, SharedBtnComponent, NgClass],
  templateUrl: './ad-addons-widget.component.html',
  styleUrl: './ad-addons-widget.component.scss'
})

export class AdAddonsWidgetComponent {

  @Input() addon?: AdsAndAddOns
  @Input() disabled = false;
  @Input() showReadMore = true;
  @Input() reviewMode = false;

  @Output() readMore = new EventEmitter<void>();
  @Output() add = new EventEmitter<void>();

  onReadMore() { this.readMore.emit(); }
  onAdd() { if (!this.disabled) this.add.emit(); }

}