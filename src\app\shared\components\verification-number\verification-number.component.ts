import { <PERSON><PERSON><PERSON>, NgIf } from '@angular/common';
import { Component, OnInit, ViewChild, ViewEncapsulation } from '@angular/core';
import { Router } from '@angular/router';
import { AuthService } from '@services/auth.service';
import { CommonService } from '@services/common.service';
import { ProfileService } from '@services/profile.service';
import { NgxOtpInputComponent, NgxOtpInputComponentOptions } from 'ngx-otp-input';
import { Button } from 'primeng/button';
import { Subscription, finalize, take, timer } from 'rxjs';
import { RegisterUserWithOTP } from 'src/app/authentication/models/dto/user.dto.model';
import { AlertHandlerService } from 'src/app/modules/core/alerts/alert-handler.service';
import { BrowserService } from 'src/app/modules/core/service/browser.service';
import { TranslationService } from 'src/app/modules/core/service/translation.service';
import { ModalService } from 'src/app/shared/services/modal/modal.service';
import { FormUtils } from 'src/utils/form-utils';
import { ConversionEvents } from '../../constants/conversion-events';
import { NtranslatePipe } from '../../pipes/ntranslate.pipe';
import { DeviceDetectionService } from '../../services/device-detection.service';
import { SecondaryBtnComponent } from '../secondary-btn/secondary-btn.component';



@Component({
  selector: 'app-verification-number',
  templateUrl: './verification-number.component.html',
  styleUrls: ['./verification-number.component.scss'],
  encapsulation: ViewEncapsulation.None,
  standalone: true,
  imports: [
    NgxOtpInputComponent,
    NgClass,
    NgIf,
    Button,
    SecondaryBtnComponent,
    NtranslatePipe,
  ],
})
export class VerificationNumberComponent implements OnInit {
  @ViewChild('ngxotp') ngxOtp: NgxOtpInputComponent;
  countDown: Subscription;
  counter = 60;
  tick = 1000;
  phoneNumber: string;
  wrongOTP: boolean = false;
  loading: boolean = false;
  otpFilled: boolean = false;
  otpInputConfig: NgxOtpInputComponentOptions = {
    otpLength: 4,
    autoFocus: true,

  };
  filledOtp: string = '';
  tryMode = false;
  constructor(
    private _modalService: ModalService,
    private _authService: AuthService,
    private _profileService: ProfileService,
    private _router: Router,
    private alertService: AlertHandlerService,
    private translateService: TranslationService,
    private commonService: CommonService,
    private browser: BrowserService,
    private device: DeviceDetectionService
  ) { }

  ngOnInit(): void {
    this.phoneNumber = this.browser.getStorageItem('phone') ?? '';

    this.resetCounter();
    this.sendOTPRegistration();
  }
  resendClicked() {
    if (this.counter > 0) return;

    this.resetCounter();
    this.sendOTPRegistration();
  }

  forceResend() {
    this.resetCounter();
    this.sendOTPRegistration();
  }

  resetCounter() {
    if (this.countDown) {
      this.countDown.unsubscribe();
    }

    this.counter = 60;
    this.tick = 1000;
    this.countDown = timer(0, this.tick)
      .pipe(take(this.counter))
      .subscribe(() => {
        --this.counter;
        // console.log(this.counter);
        if (this.counter == 0) {
          this.countDown.unsubscribe();
        }
      });

  }
  sendOTPRegistration() {


    this.wrongOTP = false;
    this._authService
      .generateotpforregistration(this.phoneNumber)
      .subscribe({
        next: (x) => {
          if (x == true) {
            //this.handleFillEvent('Pass');
          }
        },
        error: (x) => {
          this.commonService.pushDataLayer({
            'event': 'website_error',
            'error_type': 'registration otp error',
            'error_message': JSON.stringify(x)
          });
          if (x == "InvalidPhoneNumber") {
            this.alertService.warn({ message: this.translateService.instant(x) }, (e) => {
              //this.handleFillEvent('Pass');
              this._router.navigate(['/home'], { replaceUrl: true });
            });
          } else if (x.errors && x.errors.length > 0) {
            this.alertService.warn({ message: this.translateService.instant(x.errors[0] ?? '') }, (e) => {

              if (x.errors[0] == "MaxOTPTrialsReached") {
                this._router.navigate(['/home'], { replaceUrl: true });

              }

              //this.handleFillEvent('Pass');
              //this._router.navigate(['/authentication/register/interests']);
            });
          } else {
            this.alertService.warn({ message: JSON.stringify(x) }, (e) => {
              //this.handleFillEvent('Pass');
            });
          }
        }
      });


  }
  testSaveIMG() {
    //debugger;
    let image = this.browser.getStorageItem('image');
    let file = this.dataURLtoFile(image, 'image.jpg');
    let uploadData = new FormData();

    uploadData.append('userprofile', file, file.name);
    uploadData.append('id', '55a452fc-fdcb-4d36-b1c1-848e280436a7');
    uploadData.append('otp', '624321');
    this._profileService.uploadImgsWithOTP(uploadData).subscribe();
  }
  transform(value: number): string {
    const minutes: number = Math.floor(value / 60);
    return (
      ('00' + minutes).slice(-2) +
      ':' +
      ('00' + Math.floor(value - minutes * 60)).slice(-2)
    );
  }

  showDialog() {
    this._modalService.openModal();
  }

  handeOtpChange(value: string[]): void {

    this.filledOtp = value.join('');
  }

  handleOtpFill(value) {

  }



  tryVerify() {

    this.tryMode = true;

    this.handleFillEvent(this.filledOtp);

  }

  handleFillEvent(value: any): void {





    if (value == '') return;

    this.filledOtp = value;

    this.otpFilled = true;

    value = FormUtils.fixNumbers(value);

    let user = {
      phoneNumber: this.browser.getStorageItem('phone'),
      userName: this.browser.getStorageItem('phone'),
      firstName: this.browser.getStorageItem('firstName'),
      lastName: this.browser.getStorageItem('lastName'),
      areaID: FormUtils.safeNumber(this.browser.getStorageItem('city')) ?? 2,
      otp: value,
      password: this.browser.getStorageItem('password'),
      ReferredByCode: null,
      AccountType: this.browser.getStorageItem('AccountType'),
      defaultLang: 'ar-eg',
      Platform: this.device.isMobile ? 'web mobile' : 'web',
      OSVersion: this.commonService.detectBrowserAndOS()

    } as unknown as RegisterUserWithOTP;

    if (this.browser.getStorageItem('referralCode')) {
      user.ReferredByCode = this.browser.getStorageItem('referralCode') ?? '';
    }

    if (this.browser.getStorageItem('accountSource')) {
      user.AccountSource = this.browser.getStorageItem('accountSource') ?? '';
    }

    this.loading = true;


    this._authService
      .registerUserWithOTP('api/account/registration', user).pipe(finalize(() => {
        this.loading = false;
        this.tryMode = false;

      }))
      .subscribe({
        next: (x) => {
          if (!x.isSuccessfulRegistration) {
            if (x.errors && x.errors.length > 0) {
              this.alertService.warn({ message: this.translateService.instant(x.errors[0] ?? '') }, (e) => {
                this.ngxOtp.reset();
                this.resetCounter();
              });

            }
            //this.wrongOTP = !x.isSuccessfulRegistration;
            return;
          }

          this.commonService.pushDataLayer({
            'event': ConversionEvents.Register,
            'authentication_method': this.device.isMobile ? 'mobile' : 'desktop',
            'seller_id': x.userID.toString(),
            'seller_identifier': btoa(user.phoneNumber ?? '')

          });


          this.browser.removeStorageItem('referralCode');
          this.browser.removeStorageItem('accountSource');
          this.browser.removeStorageItem('phone');
          this.browser.removeStorageItem('lastName');
          this.browser.removeStorageItem('city');
          this.browser.removeStorageItem('password');
          this.browser.setStorageItem('tempOTP', x.tempToken);
          this.browser.setStorageItem('userID', x.userID);
          let image = this.browser.getStorageItem('image');


          if (image) {
            let file = this.dataURLtoFile(image, 'image.jpg');
            let uploadData = new FormData();
            uploadData.append('userprofile', file, file.name);
            uploadData.append('id', x.userID.toString());
            uploadData.append('otp', x.tempToken);
            this._profileService.uploadImgsWithOTP(uploadData).subscribe((x) => {
              this.browser.removeStorageItem('image');
            });
          }

          if (x.isVerifySMSBlocked) {
            this.alertService.warn({ message: this.translateService.instant("VerifySMSBlocked") }, (e) => {
              this._router.navigate(['/home'], { replaceUrl: true });
            });
          } else {
            this._router.navigate(['/authentication/register/interests'], { replaceUrl: true });

          }
        }, error: (x) => {

          this.commonService.pushDataLayer({
            'event': 'website_error',
            'error_type': 'registration error',
            'error_message': JSON.stringify(x)
          });

          if (x == "InvalidPhoneNumber") {
            this.alertService.warn({ message: this.translateService.instant(x) }, (e) => {
              this._router.navigate(['/home'], { replaceUrl: true });
            });
          } else if (x.errors && x.errors.length > 0) {
            this.resetCounter();
            this.alertService.warn({ message: this.translateService.instant(x.errors[0] ?? '') }, (e) => {

              if (x.errors[0] == "MaxOTPTrialsReached") {
                this._router.navigate(['/home'], { replaceUrl: true });
              }

            });
          } else {
            this.alertService.warn({ message: JSON.stringify(x) }, (e) => {

            });
          }
          this.loading = false;
        }
      });
  }

  dataURLtoFile(dataurl, filename) {
    var arr = dataurl.split(','),
      mime = arr[0].match(/:(.*?);/)[1],
      bstr = atob(arr[arr.length - 1]),
      n = bstr.length,
      u8arr = new Uint8Array(n);
    while (n--) {
      u8arr[n] = bstr.charCodeAt(n);
    }
    return new File([u8arr], filename, { type: mime });
  }
}
