.ratings-wrapper {
  display: flex;
  flex-direction: column;
  gap: 20px;
  padding: 24px;

  .ratings-header {
    display: flex;
    align-items: center;
    gap: 12px;

    .ratings-title {
      font-size: 16px;
      font-weight: 800;
      color: #272728;
    }
  }

  .ratings-summary {
    display: flex;
    flex-direction: column;
    gap: 4px;

    .summary-info {
      display: flex;
      align-items: center;
      gap: 8px;

      .stars {
        color: #faaf40;
        font-size: 14px;

        .rating-count {
          display: flex;
          gap: 4px;
          font-size: 10px;
          color: #a3a4a5;
          font-weight: 700;
        }
      }

      .total-rating {
        font-size: 26px;
        font-weight: 800;
        color: #272728;
      }
    }

    .distribution {
      display: flex;
      flex-direction: column;
      gap: 8px;

      .row {
        display: flex;
        align-items: center;
        gap: 8px;

        .count {
          width: 30px;
          font-size: 12px;
          color: #7c7c7c;
          font-weight: 700;
          text-align: center;
        }

        .bar {
          flex: 1;
          background-color: #d9d9d9;
          height: 6px;
          border-radius: 100px;
          overflow: hidden;
          padding-right: 0px;

          .fill {
            background-color: #faaf40;
            height: 100%;
            border-radius: 100px;
          }
        }

        .star-display {
          width: 31px;
          display: flex;
          justify-content: center;
          align-items: center;
          gap: 4px;

          .star-icon {
            position: relative;
          }

          .star-number {
            color: var(--Gray-2, #7c7c7c);
            font-size: 12px;
            font-weight: 700;
            line-height: 15.5px;
            word-wrap: break-word;
          }
        }
      }
    }
  }

  .review {
    display: flex;
    flex-direction: column;
    gap: 8px;

    .stars {
      color: #faaf40;
      font-size: 14px;
    }

    .meta {
      display: flex;
      justify-content: space-between;
      font-size: 10px;
      color: #7c7c7c;

      .user {
        font-size: 14px;
        font-weight: 700;
        color: #272728;
      }

      .time {
        font-size: 10px;
        color: #7c7c7c;
        font-weight: 700;
      }
    }

    .ad {
      display: flex;
      gap: 4px;
      font-size: 12px;
      color: #7c7c7c;

      .label {
        font-weight: 700;
      }

      .value {
        font-size: 12px;
        font-weight: 600;
      }
    }

    .comment {
      .label {
        font-weight: 700;
        font-size: 12px;
        color: #7c7c7c;
      }

      .text {
        font-weight: 600;
        font-size: 12px;
        color: #7c7c7c;
      }
    }

  }
}
hr {
  border: none;
  height: 0px;
  border-top: 1px solid rgba(114, 34, 130, 0.1);
  margin: 0px;
  margin-top: 8px;
}