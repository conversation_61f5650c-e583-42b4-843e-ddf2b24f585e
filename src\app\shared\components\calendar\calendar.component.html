<div class="date-filter">
    <h3>{{title}}</h3>

    <div class="quick-buttons" *ngIf="showButtons">
        <button type="button" (click)="selectToday()" [ngClass]="{ active: selected === 'today' }">
            اليوم
        </button>
        <button type="button" (click)="selectThisWeek()" [ngClass]="{ active: selected === 'week' }">
            هذا الشهر
        </button>
        <button type="button" (click)="selectThisMonth()" [ngClass]="{ active: selected === 'month' }">
            هذا الاسبوع
        </button>
    </div>

    <p-calendar [(ngModel)]="internalDates" selectionMode="range" dateFormat="dd/mm/yy" inline="true"
        [readonlyInput]="true" (ngModelChange)="onDateSelected($event)"></p-calendar>
</div>