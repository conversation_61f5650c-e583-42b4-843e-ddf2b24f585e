<app-dynamic-header *ngIf="svc.stage() !==AdsStage.Success" [title]="svc.titlesMap[svc.stage()]"
    [showBackButton]="!svc.isFirst()" (backButtonClicked)="svc.backStage()" />
<ng-container [ngSwitch]="svc.stage()">
    <ng-container *ngSwitchCase="AdsStage.Picker">
        <div><app-ad-addons-picker /></div>
    </ng-container>

    <ng-container *ngSwitchCase="AdsStage.Details">
        <div><app-pyament-invoice-ads-summary /></div>
    </ng-container>

    <ng-container *ngSwitchCase="AdsStage.Invoice">
        <div><app-pyament-invoice-ads-summary /></div>
    </ng-container>

    <ng-container *ngSwitchCase="AdsStage.ExtendDuration">
        <div><app-extend-duration-component /></div>
    </ng-container>
    <ng-container *ngSwitchCase="AdsStage.Success">
        <div><app-success-popup-ad-addons /></div>
    </ng-container>
    <ng-container *ngSwitchDefault></ng-container>
</ng-container>