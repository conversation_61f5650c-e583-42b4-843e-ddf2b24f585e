// import * as amplitude from '@amplitude/analytics-browser';
import { DOCUMENT } from '@angular/common';
import { afterNextRender, Component, inject } from '@angular/core';
import { ActivatedRoute, NavigationEnd, Router, RouterOutlet, Scroll } from '@angular/router';
import { Store } from '@ngrx/store';
import { environment } from '@src/environments/environment';
import { OAuthService } from 'angular-oauth2-oidc';
import { NgProgressbar } from 'ngx-progressbar';
import { NgProgressRouter } from 'ngx-progressbar/router';
import { PrimeNGConfig } from 'primeng/api';
import { filter, map } from 'rxjs';
import { authCodeFlowConfig } from './auth/auth.config';
import { User } from './authentication/models/dto/user.dto.model';
import { SideMenuService } from './layout/top-nav/service/side-menu.service';
import { BrowserService } from './modules/core/service/browser.service';
import { NetworkStatusComponent } from './shared/components/network-status/networl-status.component';
import { ConversionEvents } from './shared/constants/conversion-events';
import { AppcenterService } from './shared/services/appcenter.service';
import { CommonService } from './shared/services/common.service';
import { LogsService } from './shared/services/consolelogs/logs.service';
import { DeviceDetectionService } from './shared/services/device-detection.service';
import { SettingsFacade } from './shared/services/setting-facad.service';
import { loadAds, loadCategories, setLikeItem, setUser } from './store/app/actions/app.actions';



@Component({
  selector: 'app-root',
  standalone: true,
  imports: [RouterOutlet, NgProgressbar, NgProgressRouter, NetworkStatusComponent],
  templateUrl: './app.component.html',
  styleUrl: './app.component.scss'
})
export class AppComponent {
  user: User;
  title = '4swapp';
  direction: string = 'ltr';

  socialProvider: string;

  blacklistedWords = ['advertisement', 'listing', 'profile'];
  blacklistedWords2 = ['offers', 'listing', 'profile'];

  logger = inject(LogsService);

  document = inject(DOCUMENT) as Document;

  constructor(
    private activatedRoute: ActivatedRoute,
    public router: Router,
    private primengConfig: PrimeNGConfig,
    private store: Store,
    private browser: BrowserService,
    private _langService: AppcenterService,
    private oauthService: OAuthService,
    private commonService: CommonService,
    private device: DeviceDetectionService,
    private menuService: SideMenuService,
    private settings: SettingsFacade

  ) {

    this.store.dispatch(loadAds());
    this.settings.loadSettings();
    this.store.dispatch(loadCategories());

    if (this.browser.isBrowser()) {
      this.initializeBrowserOAuth();
    }



    afterNextRender(() => {
      // amplitude.init(environment.amplitude, { "autocapture": true, serverZone: 'EU' });
      this.browser.addClassToBody('app_ready');

      this.menuService.isListingDetailsPage(this.blacklistedWords.some(word => location.pathname.includes(word)));
      this.menuService.unSearchBarPage(this.blacklistedWords2.some(word => location.pathname.includes(word)));

      this.router.events
        .pipe(
          filter((event) => {
            if ((event instanceof Scroll && event.routerEvent instanceof NavigationEnd)) {
              return true;
            }
            return event instanceof NavigationEnd
          }),

          map(event => {
            if ((event instanceof Scroll && event.routerEvent instanceof NavigationEnd)) {
              return event.routerEvent as NavigationEnd;
            }
            return event as NavigationEnd;
          })
        )
        .subscribe(event => {

          this.menuService.isListingDetailsPage(this.blacklistedWords.some(word => event.url.includes(word)));
          this.menuService.unSearchBarPage(this.blacklistedWords2.some(word => event.url.includes(word)));
          this.menuService.close();

        });
    });

  }

  private initializeBrowserOAuth(): void {

    this.checkAndSaveTokenFromUrl();

    this.oauthService.configure(authCodeFlowConfig);
    this.oauthService.customQueryParams = {
      lang: this._langService.lang,
      redirectUri: environment.clintURL + "/" + this._langService.lang + '/login'
    };
    this.oauthService.setupAutomaticSilentRefresh();

    this.oauthService.events.subscribe(event => {
      this.logger.log('OAuth Event:', event.type, event);

      if (event.type === 'token_received' || event.type === 'silently_refreshed' || event.type === 'token_expires') {
        const accessToken = this.oauthService.getAccessToken();
        this.logger.log('Token from event:', !!accessToken);
        if (accessToken) {
          this.saveTokenToCookie(accessToken);
        }
      }
    });

    this.oauthService.loadDiscoveryDocumentAndTryLogin().then(() => {
      this.logger.log('Discovery document loaded and login attempted');

      const accessToken = this.oauthService.getAccessToken();
      const hasValidToken = this.oauthService.hasValidAccessToken();

      this.logger.log('After loadDiscoveryDocumentAndTryLogin:');
      this.logger.log('- Access token exists:', !!accessToken);
      this.logger.log('- Has valid access token:', hasValidToken);
      this.logger.log('- Current URL:', window.location.href);

      if (accessToken) {
        this.logger.log('Saving token to cookie from loadDiscoveryDocumentAndTryLogin');
        if (this.browser.isBrowser()) {
          document.cookie = 'access_token=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;';
        }
      } else {
        const urlToken = this.extractTokenFromUrl();
        if (urlToken) {
          this.logger.log('Found token in URL, saving to cookie');
          this.saveTokenToCookie(urlToken);
        }
      }

      if (!hasValidToken) {
        this.logger.log('No valid access token found, clearing cookie');
        this.saveTokenToCookie('');
      }


      if (!this.oauthService.hasValidAccessToken()) {
        this.logger.log('No valid access token, attempting silent refresh');
        return this.oauthService.silentRefresh();
      }
      return null;
    }).then((e) => {
      this.logger.log('Silent refresh result:', e);
      if (this.oauthService.hasValidAccessToken()) {
        const tokenAfterRefresh = this.oauthService.getAccessToken();
        this.logger.log('Token after silent refresh:', !!tokenAfterRefresh);
        if (tokenAfterRefresh) {
          this.saveTokenToCookie(tokenAfterRefresh);
        }

        this.oauthService.loadUserProfile().then((userProfile) => {
          this.logger.log('User profile loaded:', userProfile);

          if (userProfile['info']) {
            this.setUserData(userProfile['info']);
          }
        }).catch(error => {
          this.logger.error('Error loading user profile:', error);
        });
      }
    }).catch(error => {
      this.logger.error('OAuth setup error:', error);
    });
  }

  setUserData(userData: any = null) {

    if (userData == null) return;

    this.store.dispatch(setLikeItem());

    this.browser.setStorageItem('id', userData.id);
    this.browser.setStorageItem('phone', userData.phone_number);
    this.browser.setStorageItem('name', userData.firstname + ' ' + userData.lastname);

    this.setUser(userData);
    let postLoginRedirectUri = this.browser.getStorageItem('redirectUri');
    if (postLoginRedirectUri) {
      this.browser.removeStorageItem('redirectUri')
      this.router.navigateByUrl(postLoginRedirectUri);
    }
  }

  saveTokenToCookie(accessToken: string) {
    if (this.browser.isBrowser()) {
      this.logger.log('Attempting to save token to cookie:', accessToken ? 'Token present' : 'No token');

      const expiryDate = new Date();
      expiryDate.setDate(expiryDate.getDate() + 30);

      document.cookie = `access_token=${accessToken}; expires=${expiryDate.toUTCString()}; path=/; SameSite=Lax`;
      this.logger.log('Access token saved to cookie');

      const cookies = document.cookie;
      this.logger.log('Current cookies:', cookies);
      const tokenMatch = cookies.match(/access_token=([^;]+)/);
      this.logger.log('Token found in cookie:', !!tokenMatch);
    }
  }

  extractTokenFromUrl(): string | null {
    if (this.browser.isBrowser()) {
      const hash = window.location.hash;
      this.logger.log('Current URL hash:', hash);

      if (hash && hash.includes('access_token=')) {
        const match = hash.match(/access_token=([^&]+)/);
        return match ? match[1] : null;
      }
    }
    return null;
  }

  checkAndSaveTokenFromUrl(): void {
    if (this.browser.isBrowser()) {
      const urlToken = this.extractTokenFromUrl();
      if (urlToken) {
        this.logger.log('Found access token in URL during app init, saving to cookie');
        this.saveTokenToCookie(urlToken);

        if (window.history && window.history.replaceState) {
          const cleanUrl = window.location.pathname + window.location.search;
          window.history.replaceState({}, document.title, cleanUrl);
        }
      }
    }
  }

  setUser(userProfile) {


    const user: User = new User();
    user.userName = userProfile.userName;
    user.name = userProfile.firstname + ' ' + userProfile.lastname;
    user.role = userProfile.role;
    user.rate = userProfile.rating;
    user.PhoneNumber = userProfile.phone_number;
    user.defaultLanguage = userProfile.language;
    user.userImage = userProfile.userImage;
    user.id = userProfile.uid;
    user.userID = userProfile.uid;
    user.businessName = userProfile.businessName ? userProfile.businessName : null;
    user.email = userProfile.email;
    user.setting = userProfile.settings ? JSON.parse(userProfile.settings) : null;
    user.phoneConfirmed = userProfile.phone_number_confirmed?.toLowerCase() == 'true';


    this.store.dispatch(setUser({ user: user }));


  }



  ngOnInit(): void {

    if (this.browser.isBrowser()) {
      const locationVal = window.location;

      const isExternalProvider = this.commonService.getParameterByName('externalprovider');
      const isNewUser = this.commonService.getParameterByName('isnewuser');


      if (isExternalProvider && isExternalProvider.length > 0) {
        this.commonService.pushDataLayer({
          'event': ConversionEvents.Register,
          'authentication_method': this.device.isMobile ? 'mobile' : 'desktop',
        }, () => {

        });

        this.commonService.pushDataLayer({
          'event': 'social_registration',
          'authentication_method': this.device.isMobile ? 'mobile' : 'desktop',
          'provider': isExternalProvider,
        });

      } else if (isNewUser && isNewUser.length > 0 && isNewUser == "False") {
        if (isNewUser) {
          this.commonService.pushDataLayer({
            'event': ConversionEvents.Login,
            'authentication_method': this.device.isMobile ? 'social_mobile' : 'social_desktop',
          }, () => {

          });
        }


      } else if (locationVal.href.indexOf('id_token') > -1) {
        this.commonService.pushDataLayer({
          'event': ConversionEvents.Login,
          'authentication_method': this.device.isMobile ? 'mobile' : 'desktop',
        });

      }


      this.primengConfig.ripple = true;


      const htmlTag = document.getElementsByTagName(
        'html'
      )[0] as HTMLHtmlElement;
      const info = this._langService.info();
      htmlTag.dir = info.dir
      htmlTag.lang = info.lang;

      this.direction = info.dir;


      this.activatedRoute.queryParams.subscribe(res => {
        if (res['source']) {
          if (this.browser.isBrowser()) {
            localStorage.setItem('accountSource', res['source']);

          }
        }

        if (res['appview']) {
          if (this.browser.isBrowser()) {
            document.body.classList.add('appview');

          }
        }
      });


    }


  }




}
