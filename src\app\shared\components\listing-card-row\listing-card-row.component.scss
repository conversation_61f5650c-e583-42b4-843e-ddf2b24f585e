@import 'variables';

.ad-card {
    background-color: $greyBackground;
    border-radius: $radius-md;
    overflow: hidden;
    padding: 12px;
    direction: inherit;
    display: flex;
    flex-direction: column;
    margin-bottom: 1.5rem;
    cursor: pointer;
    gap: 0.4rem;

    &.selected {
        background-color: rgba($primary, 0.1);
    }
}

.ad-card-content {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    gap: 1rem;
}

.ad-card-right {
    display: flex;
    gap: 5px;

    img {
        width: 92px;
        height: 92px;
        object-fit: cover;
        border-radius: 3px;
    }
}

.ad-card-left {
    display: flex;
    justify-content: space-between;
    width: 100%;
    align-items: flex-start;
    flex-direction: column;
}

.ad-card-left-item {
    display: flex;
    align-items: flex-start;
    width: 100%;
}

.ad-header {
    margin-bottom: 10px;
    width: 100%;

    h3 {
        font-weight: 800;
        font-size: 16px;
        margin-bottom: 0px;
    }
}

.metrics {
    display: flex;
    margin-bottom: 5px;
    justify-content: space-between;
    width: 100%;
}

.metric {
    display: flex;
    flex-direction: column;
    align-items: center;

    .value {
        font-weight: bold;
        color: rgba(124, 124, 124, 1);
        margin-right: 0.1rem;
        font-size: 14px;
    }

    .label {
        color: $text-muted-1;
        font-size: 12px;

    }

    .icon {
        font-size: 14px;
        color: $warning;
    }
}

.tags {
    display: flex;
    gap: 6px;
    flex-wrap: wrap;
}

::ng-deep app-svg-icons[name="back-icon"] svg {
    transform: rotate(var(--arrow-rotation));
}

.custom-checkbox {
    position: relative;
    display: inline-block;
    width: 20px;
    height: 20px;

    input {
        opacity: 0;
        width: 0;
        height: 0;
        position: absolute;
    }

    .checkmark {
        position: absolute;
        top: 0;
        right: 0;
        height: 20px;
        width: 20px;
        background-color: $grey-2;
        border-radius: 50%;
        border: 1px solid $line;
        transition: all 0.2s ease;

        &::after {
            content: "";
        }
    }

    input:checked+.checkmark {
        background-color: $primary;
        border-color: $primary;

        &::after {
            content: "✓";
            position: absolute;
            right: 4px;
            top: 0;
            color: white;
            font-size: 14px;
        }
    }
}

.tag-label {
    background-color: rgba($muted-1, 0.1);
    color: rgba(124, 124, 124, 1);
    padding: 3px 8px;
    border-radius: 3px;
    font-size: $font-size-s;
    margin-right: 4px;
    display: inline-block;

}