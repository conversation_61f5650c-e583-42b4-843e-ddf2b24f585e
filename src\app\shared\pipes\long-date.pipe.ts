import { Pipe, PipeTransform } from '@angular/core';
import 'moment/locale/ar';
import 'moment-timezone';
import { moment } from 'swappmoment';
import { AppcenterService } from '../services/appcenter.service';

@Pipe({
  name: 'LongDate',
  standalone: true,
})
export class LongDatePipe implements PipeTransform {
  constructor(private languageService?: AppcenterService) {}


  transform(
    value: string | number | Date | null | undefined,
    pattern: string = 'D MMMM YYYY',
    arabicDigits: boolean = false
  ): string {
    
    if (!value) return '';

    const lang = (this.languageService?.lang || 'ar').toLowerCase();

    const m = moment.utc(value).tz('Africa/Cairo');
    if (!m.isValid()) return value.toString();

    const localized = m.locale(lang.startsWith('ar') ? 'ar' : lang).format(pattern);

    return arabicDigits ? localized : this.toEnglishDigits(localized);
  }

  private toEnglishDigits(s: string): string {
    const map: Record<string, string> = {
      '٠': '0', '١': '1', '٢': '2', '٣': '3', '٤': '4',
      '٥': '5', '٦': '6', '٧': '7', '٨': '8', '٩': '9',
    };
    return s.replace(/[٠-٩]/g, (d) => map[d]);
  }
}
