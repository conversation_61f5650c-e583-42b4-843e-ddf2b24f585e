.rating-container {
    display: inline-flex;
    justify-content: flex-start;
    align-items: center;
    gap: 4px;
    cursor: pointer;
}

.rating-text {
    text-align: center;
    color: var(--<PERSON>, #A3A4A5);
    font-size: 10px;
    font-family: Cairo, sans-serif;
    font-weight: 700;
    line-height: 28px;
    word-wrap: break-word;
    text-box-trim: trim-both;
    text-box-edge: cap alphabetic;
}

.prime-half-rating {
    display: inline-flex;
    align-items: center;
    gap: 4px;
    font-size: 16px;
    line-height: 1;
}

.filled {
    color: #FAAF40;
}

.empty {
    color: #d9d9d9;
}

.half {
    position: relative;
    display: inline-block;
}

.half .pi-star.empty {
    color: #d9d9d9;
}

.half .pi-star-fill.filled {
    position: absolute;
    top: 0;
    bottom: 0;
    width: 50%;
    overflow: hidden;
    inset-inline-start: 0;
}

.pi-star {
    color: #FAAF40; 
}
.half .pi-star.empty {
    color: #FAAF40; 

}
@media (max-width: 900px) {
  .half .pi-star-fill.filled {
    top: 1px;
  }
}