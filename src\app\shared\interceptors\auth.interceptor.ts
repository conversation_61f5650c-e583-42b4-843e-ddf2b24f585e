import { <PERSON>tt<PERSON><PERSON><PERSON>, <PERSON>tt<PERSON><PERSON><PERSON><PERSON>, HttpInterceptor, HttpRequest } from '@angular/common/http';
import { inject, Inject, Injectable, Optional } from '@angular/core';
import { Observable } from 'rxjs';
import { AuthTokenService } from '../services/auth-token.service';
import { SERVER_ACCESS_TOKEN } from '../services/common.service';
import { LogsService } from '../services/consolelogs/logs.service';
import { OAuthCookieStorage } from '../services/oauth-cookie-storage.service';

@Injectable()
export class AuthOIDCInterceptor implements HttpInterceptor {
    logger = inject(LogsService);
    private static requestCount = 0;
    private static readonly MAX_REQUESTS_PER_SECOND = 100;
    private static lastResetTime = Date.now();

    constructor(
        private authTokenService: AuthTokenService,
        private oauthStorage: OAuthCookieStorage,
        @Optional() @Inject(SERVER_ACCESS_TOKEN) private serverAccessToken: string | null
    ) { }

    intercept(req: HttpRequest<any>, next: Htt<PERSON><PERSON>and<PERSON>): Observable<HttpEvent<any>> {
        // Rate limiting to prevent infinite loops
        const now = Date.now();
        if (now - AuthOIDCInterceptor.lastResetTime > 1000) {
            AuthOIDCInterceptor.requestCount = 0;
            AuthOIDCInterceptor.lastResetTime = now;
        }

        AuthOIDCInterceptor.requestCount++;
        if (AuthOIDCInterceptor.requestCount > AuthOIDCInterceptor.MAX_REQUESTS_PER_SECOND) {
            this.logger.warn('Too many requests detected, possible infinite loop. Skipping auth for:', req.url);
            return next.handle(req);
        }

        const isLang = req.url.includes('api/language');

        const skipAuthUrls = [
            '/assets/',
            '.js',
            '.css',
            '.ico',
            '.png',
            '.jpg',
            '.jpeg',
            '.gif',
            '.svg',
            '.woff',
            '.woff2',
            '.ttf',
            '.eot',
            'api/language',
            'oauth',
            'auth',
            'login',
            'logout',
            '/.well-known/',
            '/discovery'
        ];

        const shouldSkipAuth = skipAuthUrls.some(skipUrl => req.url.includes(skipUrl));

        if (shouldSkipAuth) {
            return next.handle(req);
        }

        if (req.headers.has('Authorization')) {
            return next.handle(req);
        }

        let authToken = this.authTokenService.getAccessToken();

        if (!authToken) {
            authToken = this.oauthStorage.getItem('access_token');
            this.logger.log('AuthOIDCInterceptor - Using token from cookie storage:', !!authToken);
        }

        if (!authToken && this.serverAccessToken) {
            authToken = this.serverAccessToken;
            this.logger.log('AuthOIDCInterceptor - Using server access token:', !!authToken);
        }

        if (authToken && !isLang) {
            const authReq = req.clone({
                setHeaders: { Authorization: `Bearer ${authToken}` },
            });
            return next.handle(authReq);
        } else if (isLang) {
            const authReq = req.clone({
                setHeaders: { Authorization: '' },
            });
            return next.handle(authReq);
        }

        return next.handle(req);
    }
}