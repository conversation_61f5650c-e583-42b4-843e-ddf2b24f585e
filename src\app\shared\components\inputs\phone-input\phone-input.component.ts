import { CommonModule } from '@angular/common';
import { ChangeDetectorRef, Component, Inject, Injector, Input, OnDestroy, OnInit, forwardRef } from '@angular/core';
import { ControlValueAccessor, FormControl, FormControlDirective, FormControlName, FormGroupDirective, FormsModule, NG_VALUE_ACCESSOR, NgControl, NgModel, ReactiveFormsModule } from '@angular/forms';
import { DropdownModule } from 'primeng/dropdown';
import { InputNumberModule } from 'primeng/inputnumber';
import { Subject, takeUntil, tap } from 'rxjs';
import { ArabicToEnglishNumeralsDirective } from 'src/app/shared/directives/arabictoenglishnum.directive';
import { NtranslatePipe } from 'src/app/shared/pipes/ntranslate.pipe';

@Component({
  selector: 'app-phone-input',
  templateUrl: './phone-input.component.html',
  styleUrls: ['./phone-input.component.scss'],
  standalone: true,
  imports: [CommonModule, FormsModule, DropdownModule, ReactiveFormsModule, NtranslatePipe, InputNumberModule, ArabicToEnglishNumeralsDirective],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => PhoneInputComponent),
      multi: true,
    },
  ],

})
export class PhoneInputComponent implements OnInit, ControlValueAccessor, OnDestroy {
  countries: any[] = [
    { name: '+20', code: 'EG' },
    // { name: '+966', code: 'SA' },
    // { name: '+971', code: 'AE' },
  ];
  selectedCountry!: any;
  phone: string = '';
  onChange: any = () => { };
  onTouch: any = () => { };
  showValidator: boolean = false;
  inputControl: FormControl = new FormControl();
  control!: FormControl;
  readonly destroy = new Subject<void>();


  @Input() placehplder!: string;
  @Input() isReadOnly: boolean = false;


  constructor(@Inject(Injector) private injector: Injector, private readonly changeDetectorRef: ChangeDetectorRef) { }

  ngOnInit(): void {
    this.selectedCountry = this.countries[0];
    this.setComponentControl();
  }



  onInput(event) {
    const input = event.target;
    if (input.value.length > 11) {
      input.value = input.value.substr(0, 11);
    }
    const number = input.value.trim();

    const regex = /^0?(10|12|15|11)[0-9]{7}/;

    const isValid = regex.test(number);
    if (isValid) {
      const formattedNumber = number.startsWith('0') ? number : `0${number}`;
      input.value = formattedNumber;
      this.changeValue(formattedNumber);
    }
  }


  writeValue(value: any): void {
    this.phone = value;
  }
  registerOnChange(fn: any): void {
    this.onChange = fn;
  }
  registerOnTouched(fn: any): void {
    this.changeDetectorRef.detectChanges();
    this.inputControl.valueChanges.subscribe(fn);
  }
  setDisabledState?(isDisabled: boolean): void {
    isDisabled ? this.inputControl.disable() : this.inputControl.enable();
  }

  changeValue($event: any): void {
    this.onChange(this.selectedCountry.name + ',' + $event);
  }

  get hasErrors() {
    return this.control.errors != null;
  }

  setComponentControl() {
    const injectedControl = this.injector.get(NgControl);
    switch (injectedControl.constructor) {
      case NgModel: {
        const { control, update } = injectedControl as NgModel;

        this.control = control;

        this.control.valueChanges
          .pipe(
            tap((value) => update.emit(value)),
            takeUntil(this.destroy),
          )
          .subscribe();
        break;
      }
      case FormControlName: {
        this.control = this.injector.get(FormGroupDirective).getControl(injectedControl as FormControlName);
        break;
      }
      default: {
        this.control = (injectedControl as FormControlDirective).form as FormControl;
        break;
      }
    }
  }

  ngOnDestroy(): void {
    this.destroy.next();
    this.destroy.complete();
  }

}
