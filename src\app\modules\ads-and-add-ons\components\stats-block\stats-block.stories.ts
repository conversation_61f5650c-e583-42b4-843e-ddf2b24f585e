// src/stories/stats-block.stories.ts
import { Meta, StoryObj, moduleMetadata } from '@storybook/angular';
import { CommonModule } from '@angular/common';

// ⬇️ Adjust paths to match your project
import { SvgIconsComponent } from '@src/app/shared/components/svg-icons/svg-icons.component';
import { StatsBlockComponent } from './stats-block.component';

const meta: Meta<StatsBlockComponent> = {
    title: 'Ads and add ons/Stats Block',
    component: StatsBlockComponent,
    decorators: [
        moduleMetadata({
            imports: [CommonModule, SvgIconsComponent],
        }),
    ],
    argTypes: {
        stats: {
            control: 'object',
            description: 'Array of stat items { label, value, icon }',
        },
    },
    tags: ['autodocs'],
};
export default meta;

type Story = StoryObj<StatsBlockComponent>;

export const Default: Story = {
    args: {
        stats: [
            { label: "total_messages", value: 231, icon: 'message-icon' },
            { label: "total_calls", value: 120, icon: 'phone-icon' },
            { label: "total_views", value: 582, icon: 'arrow-top-left' },
            { label: "total_offers", value: 421, icon: 'tag-icon' },
            { label: "total_impressions", value: 592, icon: 'eye-icon' },
        ],
    },
};

export const WithArabicLabels: Story = {
    name: 'Arabic labels (RTL)',
    args: {
        stats: [
            { label: "total_messages", value: 524, icon: 'message-icon' },
            { label: "total_calls", value: 854, icon: 'phone-icon' },
            { label: "total_views", value: 1235, icon: 'arrow-top-left' },
            { label: "total_offers", value: 1234, icon: 'tag-icon' },
            { label: "total_impressions", value: 2345, icon: 'eye-icon' },
        ],
    },
    render: (args) => ({
        props: args,
        template: `
      <div dir="rtl">
        <h3 class="stats-title">إجمالي التفاعل</h3>
        <app-stats-block [stats]="stats"></app-stats-block>
      </div>
    `,
    }),
};

export const SomeItems: Story = {
    args: {
        stats: [
            { label: "total_messages", value: 6234, icon: 'message-icon' },
            { label: "total_calls", value: 345, icon: 'phone-icon' },
            { label: "total_views", value: 3, icon: 'arrow-top-left' },
        ],
    }
};
