import { CommonModule } from "@angular/common";
import { Component, EventEmitter, Input, Output } from "@angular/core";
import { SvgIconsComponent } from "@src/app/shared/components/svg-icons/svg-icons.component";

type btnThemes = 'noraml' | 'bgWhite' | 'error' | '';
@Component({
  selector: "app-shared-btn",
  standalone: true,
  imports: [SvgIconsComponent, CommonModule],
  templateUrl: "./shared-btn.component.html",
  styleUrl: "./shared-btn.component.scss",
})
export class SharedBtnComponent {
  @Input() label?: string = "";
  @Input() iconName?: string | null = null;
  @Input() iconWidth?: string = "";
  @Input() iconHeight?: string = "";
  @Input() size?: "small" | "medium" | "large" = "large";
  @Input() bgcolor?: string = "#722282";
  @Input() labelColor?: string = "white";
  @Input() boxShadow?: string = "#6b367f";
  @Input() disabled?: boolean = false;
  @Input() theme: btnThemes = '';

  @Output() btnClick = new EventEmitter<void>();

  onClick() {
    this.btnClick.emit();
  }
}
