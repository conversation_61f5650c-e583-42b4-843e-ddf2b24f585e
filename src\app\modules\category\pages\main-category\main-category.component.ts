import { AsyncPipe, CommonModule } from "@angular/common";
import { Component, ElementRef, OnInit, ViewChild } from "@angular/core";
import { FormsModule } from "@angular/forms";
import { ActivatedRoute, Router } from "@angular/router";
import { Store } from "@ngrx/store";
import { BrowserService } from "@src/app/modules/core/service/browser.service";
import { TranslationService } from "@src/app/modules/core/service/translation.service";
import { DesktopStripComponent } from "@src/app/shared/components/desktop-strip/desktop-strip.component";
import { MpuContainerComponent } from "@src/app/shared/components/mpu-container/mpu-container.component";
import { AdsService } from "@src/app/shared/services/ads.service";
import { getAds } from "@src/app/store/app/selectors/app.selector";
import { InfiniteScrollModule } from "ngx-infinite-scroll";
import { MenuItem, PrimeTemplate } from "primeng/api";
import { DialogModule } from "primeng/dialog";
import { DropdownModule } from "primeng/dropdown";
import { PaginatorModule } from "primeng/paginator";
import { BehaviorSubject, Observable, Subject, combineLatest, debounceTime, map, tap } from "rxjs";
import { SortBy } from "src/app/modules/core/enums";
import { Pagination } from "src/app/shared/models/base.response.model";
import { ListingFilters, ListingView } from "src/app/shared/models/listing.model";
import { AdsRoutes, AdsSectionDTO, AdsSlotName, CategoryMenuDTO, LookupDTO, StripAdsType } from "src/app/shared/models/lookup.model";
import { PreviewModel } from "src/app/shared/models/preview.model";
import { CommonService } from "src/app/shared/services/common.service";
import { DeviceDetectionService } from "src/app/shared/services/device-detection.service";
import { ListingService } from "src/app/shared/services/listing.service";
import { LookupService } from "src/app/shared/services/lookup.service";
import { FormUtils } from "src/utils/form-utils";
import { ItemCardRowComponent } from "../../../../shared/components/item-card-row/item-card-row.component";
import { ItemCardComponent } from "../../../../shared/components/item-card/item-card.component";
import { LayoutToggleBtnComponent } from "../../../../shared/components/layout-toggle-btn/layout-toggle-btn.component";
import { NoResultComponent } from "../../../../shared/components/no-result/no-result.component";
import { SideFilterComponent } from "../../../../shared/components/side-filter/side-filter.component";
import { ItemCardRowSkeletonComponent } from "../../../../shared/components/skeleton/item-card-row-skeleton/item-card-row-skeleton.component";
import { NtranslatePipe } from "../../../../shared/pipes/ntranslate.pipe";
import { CategoryFooterComponent } from "../../component/category-footer/category-footer.component";
import { NewCategoryHeaderComponent } from "../../component/new-category-header/new-category-header.component";
import { ManageCategoriesService } from "../../services/manage-categoies.service";

@Component({
  selector: "app-main-category",
  templateUrl: "./main-category.component.html",
  styleUrls: ["./main-category.component.scss"],
  standalone: true,
  imports: [
    CommonModule,
    InfiniteScrollModule,
    DialogModule,
    SideFilterComponent,
    LayoutToggleBtnComponent,
    DropdownModule,
    FormsModule,
    PrimeTemplate,
    ItemCardComponent,
    ItemCardRowComponent,
    NoResultComponent,
    PaginatorModule,
    CategoryFooterComponent,
    ItemCardRowSkeletonComponent,
    AsyncPipe,
    NtranslatePipe,
    NewCategoryHeaderComponent,
    DesktopStripComponent,
    MpuContainerComponent
  ],
})
export class MainCategoryComponent implements OnInit {
  @ViewChild("listingList") listingList: ElementRef<HTMLInputElement>;

  AdsSlotName = AdsSlotName;

  filterResults$ = new Subject();

  isGrid: boolean = false;
  isLoading = false;

  isCollapsed = true;

  isCollapsed2 = true;
  filterVisible: boolean = false;

  sortBy: SortBy[] = [
    {
      value: "firstApprovalDate",
      viewValue: this.translateService.instant("Newest"),
      isSortAscending: false,
    },
    {
      value: "price",
      viewValue: this.translateService.instant("HighPrice"),
      isSortAscending: false,
    },
    {
      value: "price",
      viewValue: this.translateService.instant("LowPrice"),
      isSortAscending: true,
    },
  ];

  selectedSortBy: SortBy;

  pagination: Pagination = {
    currentPage: 1,
    pageSize: 12,
    totalItems: 0,
    totalPages: 0,
  };
  userParams: any = {
    sortBy: "firstApprovalDate",
    isSortAscending: false,
    name: "",
  };
  searchString: string;
  locationString: string;
  listings$ = new BehaviorSubject<ListingView[]>([]);
  categoryId: number;
  subCategoryId: number;
  category$: Observable<LookupDTO>;

  filter: ListingFilters = {
    status: 2,
  };

  category: LookupDTO;
  parentCategory: LookupDTO;

  categoriesList: MenuItem[];
  selectedCategoryId: number;

  selectedProperties = [];

  recommendedlisting$: Observable<PreviewModel[]>;

  page: number = 1;

  categoryPageTitle: string = "";

  categoryInfo!: CategoryMenuDTO;
  adWithIndexes: Map<number, AdsSectionDTO[]> = new Map<number, AdsSectionDTO[]>();
  constructor(
    private _listingService: ListingService,
    private _lookupService: LookupService,
    private activatedRoute: ActivatedRoute,
    private route: Router,
    public deviceDetection: DeviceDetectionService,
    private browser: BrowserService,
    private commonService: CommonService,
    private adsService: AdsService,
    private manageCategories: ManageCategoriesService,
    private translateService: TranslationService,
    private store: Store
  ) {
    this.adsService.setAdsRoute(AdsRoutes.Category);
    this.adsService.setAdsStripType(StripAdsType.Category);
  }

  ngOnInit(): void {
    this.filterResults$.pipe(debounceTime(500)).subscribe(() => {
      this.getCategoryMap();
    });

    combineLatest([this.activatedRoute.params, this.activatedRoute.queryParams])
      .pipe(
        map(([params, queryParams]) => {
          this.categoryInfo =
            this.activatedRoute.snapshot.data["categoryResolver"];

          this.categoryId = +params["id"];
          this.subCategoryId = isNaN(Number(params["sub"]))
            ? 0
            : +params["sub"];

          this.selectedCategoryId = this.subCategoryId
            ? this.subCategoryId
            : this.categoryId
              ? this.categoryId
              : 0;

          if (this.filter) {
            delete this.filter.conditions;
            delete this.filter.payments;
            delete this.filter.priceFrom;
            delete this.filter.priceTo;
            delete this.filter.selectedProperties;
          }

          if (this.selectedCategoryId) {
            this.categoriesList = this._lookupService.convertToMenuItems([
              this.categoryInfo,
            ]);
          }

          if (queryParams["pageNumber"]) {
            for (let obj in queryParams) {
              if (obj == "selectedProperties") {
                this.filter[obj] = JSON.parse(atob(queryParams[obj]));
              } else {
                this.filter[obj] =
                  obj == "conditions"
                    ? +queryParams[obj] > 0
                      ? [+queryParams[obj]]
                      : []
                    : FormUtils.parseValue(queryParams[obj]);
              }
            }
            this.pagination.currentPage = +queryParams["pageNumber"];
            if (this.browser.isBrowser()) {
              setTimeout(() => {
                window.scrollTo({
                  behavior: "smooth",
                  top: this.listingList?.nativeElement.offsetTop - 100,
                });
              }, 0);
            }
          } else {
            this.pagination.currentPage = 1;
            this.filter = {
              ...this.filter,
              sortBy: this.userParams.sortBy,
              isSortAscending: this.userParams.isSortAscending,
            };
          }

          this.selectedSortBy = this.sortBy.find(
            (item) =>
              item.value == this.filter.sortBy &&
              item.isSortAscending == this.filter.isSortAscending
          );

          if (this.browser.isBrowser()) {
            this.store.select(getAds).pipe(map(e => e.filter(item => item.routeName == 'Category'))).subscribe((ads) => {


              if (ads && ads.length > 0) {
                ads.forEach(ad => {


                  if (ad && ad.configurations && (ad.locationName.includes('ListingCustomMpu') || ad.locationName.includes('ListingRegularMpu'))) {

                    const configs = ad.configurations.reduce((acc, config) => {
                      acc[config.configKey] = config.configValue;
                      return acc;
                    }, {});
                    const adblockPositions = configs['adblock_positions'];
                    if (adblockPositions) {
                      const positions = adblockPositions
                        .split(',')
                        .map(pos => parseInt(pos.trim(), 10) - 1)
                        .filter(index => index >= 0);

                      positions.forEach(index => {
                        if (this.adWithIndexes.has(index)) {
                          this.adWithIndexes.get(index)!.push(ad);
                        } else {
                          this.adWithIndexes.set(index, [ad]);
                        }
                      });

                    }
                  }
                });


              }
            });
          }


          this.filterResults$.next(true);
        })
      )
      .subscribe();
  }

  changeGridMode(value) {
    this.isGrid = value;
  }

  onSortingChanged() {
    this.filter = {
      ...this.filter,
      sortBy: this.selectedSortBy?.value ?? this.userParams.sortBy,
      isSortAscending:
        this.selectedSortBy?.isSortAscending ?? this.userParams.isSortAscending,
    };

    if (this.filter.selectedProperties) {
      this.filter.selectedProperties = btoa(
        JSON.stringify(this.filter.selectedProperties)
      );
    }

    if (this.pagination.currentPage > 1) {
      this.filter.pageNumber = 1;
      this.changeRoute([]);
    } else {
      this.changeRoute([]);
    }
  }

  getCategoryMap() {
    const { category, parent } = this._lookupService.findCategoryById(
      this.selectedCategoryId,
      this.categoriesList
    );
    this.category = category;
    this.parentCategory = parent;
    this.manageCategories.setCategory(this.category);

    if (this.category?.queryParams) {
      this.categoryPageTitle =
        this.category?.queryParams["pageTitle"] ??
        this.category?.queryParams["name"];
      this._lookupService.updateCategoryName(
        this.category?.queryParams["name"]
      );
    }

    if (this._lookupService.selectedCategoryName.length > 0) {
      this.commonService.pushDataLayer({
        event: "ViewCategory",
        category_name: this._lookupService.selectedCategoryName,
      });
    }

    this.filter = {
      ...this.filter,
      categories: [this.selectedCategoryId],
      pageNumber: this.pagination.currentPage,
      pageSize: this.pagination.pageSize,
      search: this.searchString,
    };

    this.getListing();
  }

  pageChanged(event: any): void {
    this.filter.pageNumber = parseInt(event.page) + 1;
    if (this.filter.selectedProperties) {
      this.filter.selectedProperties = btoa(
        JSON.stringify(this.filter.selectedProperties)
      );
    }

    this.changeRoute([]);
  }

  trackby(index, item) {
    return item.label;
  }

  onSubmitFilter(e) {
    this.filterVisible = false;
    if (e.clear) {
      this.pagination.currentPage = 1;
    }

    const pTo = e.priceTo ?? 0;

    const filterProperties = e.selectedProperties?.filter(
      (item) =>
        !(
          item.type === "CheckBox" &&
          Array.isArray(item.value) &&
          item.value.length === 0
        )
    );
    this.filter = {
      ...this.filter,
      pageNumber: this.pagination.currentPage,
      pageSize: this.pagination.pageSize,
      categories: e.categories,
      priceFrom: e.priceFrom ?? 0,
      search: this.searchString,
      location: 0,
      area: 0,
      sortBy: this.selectedSortBy?.value ?? this.userParams.sortBy,
      isSortAscending:
        this.selectedSortBy?.isSortAscending ?? this.userParams.isSortAscending,
      selectedProperties: btoa(JSON.stringify(filterProperties ?? [])),
    };

    if (pTo) {
      this.filter.priceTo = pTo;
    }

    if (e.conditions) {
      this.filter.conditions = e.conditions;
    }
    if (e.payments) {
      this.filter.payments = e.payments;
    }

    if (e.location && e.location > 0) this.filter.location = e.location;
    if (e.area && e.area > 0) this.filter.area = e.area;

    if (this.pagination.currentPage > 1) {
      this.filter.pageNumber = 1;
    }
    this.changeRoute([]);
  }

  getListing() {
    this.filter = {
      ...this.filter,
      pageNumber: this.pagination.currentPage,
      pageSize: this.pagination.pageSize,
      search: this.searchString,
      sortBy: this.filter.sortBy ?? this.userParams.sortBy,
      isSortAscending:
        this.filter.isSortAscending ?? this.userParams.isSortAscending,
      status: 2,
    };

    this._listingService
      .getAll(this.filter)
      .pipe(
        tap((res) => {
          this.pagination.totalItems = res.body.data.totalCount;
          this.pagination.totalPages = res.body.data.totalPages;
        }),
        map((res) => res.body.data.items)
      )
      .subscribe((items) => {
        if (this.filter.pageNumber === 1) {
          this.listings$.next(items);
        } else {
          const currentItems = this.listings$.getValue();
          this.listings$.next([...currentItems, ...items]);
        }
      });
  }

  changeRoute(url) {
    if (this.browser.isBrowser()) {
      setTimeout(() => {
        window.scrollTo({
          behavior: "smooth",
          top: this.listingList?.nativeElement.offsetTop - 100,
        });
      }, 0);
    }
    this.route.navigate(url, { queryParams: { ...this.filter } });
  }

  onScroll() {
    if (this.isLoading) return;

    if (this.pagination.currentPage >= this.pagination.totalPages) return;

    this.isLoading = true;

    this.filter.pageNumber = (this.filter.pageNumber ?? 1) + 1;

    this._listingService
      .getAll(this.filter)
      .pipe(
        tap((res) => {
          this.pagination.totalItems = res.body.data.totalCount;
          this.pagination.totalPages = res.body.data.totalPages;
        }),
        map((res) => res.body.data.items)
      )
      .subscribe({
        next: (newItems) => {
          const oldItems = this.listings$.getValue();
          this.listings$.next([...oldItems, ...newItems]);
          this.pagination.currentPage = this.filter.pageNumber!;
          this.isLoading = false;
        },
        error: () => {
          this.isLoading = false;
        }
      })
  }
  hasAd(index) {
    return this.adWithIndexes.has(index);
  }



  ShowAd(index: number): AdsSectionDTO[] | undefined {
    if (!this.deviceDetection.isMobile) return undefined;

    const ads = this.adWithIndexes.get(index);
    if (!ads) return undefined;

    const listings = this.listings$.getValue();
    const listing = listings[index];
    if (!listing) return ads;

    return ads.length > 0 ? ads : undefined;
  }
}
