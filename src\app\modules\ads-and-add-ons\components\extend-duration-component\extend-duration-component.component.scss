@import "variables";

.header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 1rem;

    .title-wrap {
        display: flex;
        align-items: center;
        gap: 6px;

        .title {
            font-weight: 800;
            font-size: 16px;
            color: $primary;
            font-family: $f-b;
        }
    }
}

.notes {
    margin: 0 0 14px;
    padding: 0 28px 0 0;
    font-size: 14px;
    font-weight: 400;
    color: rgb(39, 39, 40);
    line-height: 2;
}

.points {
    margin: 1.5rem 0rem;
}

.meta {
    display: flex;
    flex-direction: column;
    gap: 4px;

    .row {
        display: flex;
        gap: 4px;
        font-size: 14px;
        color: $text-color;

        .label {
            font-weight: 700;
            padding: 0px;
        }

        .value {
            font-weight: 400;
        }
    }
}

.range-input {
    margin: 1.5rem 0rem;

    ::ng-deep .ads-range__header {
        font-size: 12px;
        font-weight: 600;
        color: rgba(108, 114, 120, 1);
    }
}