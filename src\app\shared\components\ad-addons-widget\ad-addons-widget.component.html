<div [ngClass]="{'promo-card': !reviewMode}">
    <div class="header">
        <div class="title-wrap">
            <app-svg-icons *ngIf="addon.iconId" [name]="addon.iconId" width="30px" color="#722282"
                height="30px"></app-svg-icons>
            <span class="title" *ngIf="addon.title">
                {{ addon.title }}
            </span>
        </div>
    </div>

    <p class="desc" *ngIf="addon.description">{{ addon.description }}</p>

    <div class="meta">
        <div class="row" *ngIf="addon.validityValue">
            <span class="label">الصلاحية: <span class="value">{{ addon.validityValue }} يومًا</span></span>

        </div>
        <div class="row" *ngIf="addon.costValue">
            <span class="label">التكلفة: {{ addon.validityValue }} <span class="value"> نقطة</span></span>
        </div>
    </div>


    <ng-container *ngIf="!reviewMode">

        <button type="button" class="read-more" (click)="onReadMore()" *ngIf="showReadMore">
            قراءة المزيد <span class="arrow" aria-hidden="true">
                <app-svg-icons name="back-icon" width="12px" height="12px"></app-svg-icons>
            </span>
        </button>

        <div *ngIf="!addon.subscribed; else notSubscribedTemp">
            <app-shared-btn label="اضافة" (btnClick)="onAdd()"></app-shared-btn>
        </div>

        <ng-template #notSubscribedTemp>
            <div class="auto-renew">
                <span>
                    <app-svg-icons width="13px" height="13px" name="renew-icon"></app-svg-icons>
                    تتجدد تلقائيًا بعد <strong>{{ addon.daysLeft }} أيام</strong>
                </span>
            </div>
            <app-shared-btn label="تمديد المدة" (btnClick)="onAdd()" theme="bgWhite"></app-shared-btn>
        </ng-template>

    </ng-container>

</div>