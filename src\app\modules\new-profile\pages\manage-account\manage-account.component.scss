@import "variables";

.profile-container {
    display: flex;
    flex-direction: column;
}

.popular-listings {
    padding: 0px 24px;
    margin-top: 24px;

    div {
        margin-bottom: 1rem;
    }
}

.emptyStateListing {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;

    span {
        font-size: 10px;
        font-weight: 700;
        color: $gray1;
    }
}

.upgrade-btn {
    display: inline-flex;
    align-items: center;
    gap: 6px;
    padding: 6px 12px;
    border: none;
    border-radius: 6px;
    background: rgba(250, 175, 64, 1);
    color: #fff;
    font-size: 10px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
}

.profile-detials {
    background-color: white;
    border-radius: 16px;
    margin-top: -1.3rem;
    z-index: 3;
}

.profile-header-business {
    margin-top: -2.5rem;
}

.profile-header-individual {
    margin-top: -4.5rem;
}

.more-settings {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 16px;
    padding: 0px 24px;

    .section-title {
        margin: 0px;
    }

    .section-footer {
        grid-column: span 2;
        background-color: #eeeeee;
        border-radius: 6px;
        padding: 20px 16px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        cursor: pointer;

        .label {
            flex: 1;
            font-size: 14px;
            font-weight: 600;
            color: #272728;
        }
    }
}

.section-title {
    display: flex;
    justify-content: space-between;
    align-items: baseline;
    font-size: 16px;
    font-weight: bold;
    margin: 10px 0px;

    .seeAll {
        cursor: pointer;
        color: $primary;
        font-weight: bold;
        font-size: 12px;
        padding-bottom: 4px;
        border-bottom: 2px solid transparent;
        transition: all 0.3s ease;
    }
}

::ng-deep app-svg-icons[name="back-icon"] svg {
    transform: rotate(var(--arrow-rotation));
}

.auto-renew {
    padding: 2px;
    font-size: 10px;
    margin-bottom: 8px;
    font-weight: bold;

    span {
        color: rgba(124, 124, 124, 1);
        margin-bottom: 0.5rem;
    }
}