// points-status-widget.stories.ts
import { Meta, StoryObj, moduleMetadata } from '@storybook/angular';
import { CommonModule } from '@angular/common';
import { PointsStatusWidgetComponent } from './points-status-widget.component';
import { SvgIconsComponent } from '../svg-icons/svg-icons.component';

const meta: Meta<PointsStatusWidgetComponent> = {
    title: 'shared/Points Status Widget',
    component: PointsStatusWidgetComponent,
    decorators: [
        moduleMetadata({
            imports: [CommonModule, PointsStatusWidgetComponent, SvgIconsComponent],
        }),
    ],
    argTypes: {
        usedPoints: { control: { type: 'number', min: 0 } },
        current: { control: { type: 'number', min: 1 } },
        pointsRequired: { control: { type: 'number', min: 0 } },
        expiresAt: { control: 'date' },
    },
    tags: ['autodocs'],
};
export default meta;

type Story = StoryObj<PointsStatusWidgetComponent>;

export const Default: Story = {
    args: {
        usedPoints: 22,
        current: 100,
        pointsRequired: 10,
        expiresAt: new Date('2025-02-11'),
    },
};
export const WithBtn: Story = {
    args: {
        usedPoints: 22,
        current: 100,
        pointsRequired: 10,
        expiresAt: new Date('2025-02-11'),
        label: 'Add Points',
        iconName: 'icon-circle-plus',
    },
};
export const WithOutIconBtn: Story = {
    args: {
        usedPoints: 22,
        current: 100,
        pointsRequired: 10,
        expiresAt: new Date('2025-02-11'),
        label: 'Extension of the term',
    },
};

export const EnoughPoints: Story = {
    args: {
        usedPoints: 750,
        current: 1000,
        pointsRequired: 200, // can buy → bar stays normal
        expiresAt: new Date('2025-12-31'),
    },
};

export const NotEnoughPoints: Story = {
    args: {
        usedPoints: 1000,
        current: 1000,
        pointsRequired: 4000, // cannot buy → bar + count turn red, details show
        expiresAt: new Date('2025-02-11'),
    },
};

export const NoRequiredCost: Story = {
    args: {
        usedPoints: 340,
        current: 1000,
        pointsRequired: 0, // hides the "will-fill" overlay
        expiresAt: new Date(),
    },
};
