import { Component, inject, Input } from '@angular/core';
import { CustomDetailsComponent } from "../custom-details/custom-details.component";
import { ViewSwitchService } from '../../services/view-switch.service';
import { SvgIconsComponent } from '@src/app/shared/components/svg-icons/svg-icons.component';

@Component({
  selector: 'app-custom-package-plan',
  standalone: true,
  imports: [CustomDetailsComponent, SvgIconsComponent],
  templateUrl: './custom-package-plan.component.html',
  styleUrl: './custom-package-plan.component.scss'
})
export class CustomPackagePlanComponent {
  @Input() duration: string = '';
  @Input() adCount: string = '';
  @Input() availablePoints: string = '';
  @Input() features: string[] = [];
  @Input() support: string = '';
  @Input() price: string = '';
  @Input() renewalNote: string = "سيتم تجديد تلقائياً بعد 90 يوم";
  private viewSvc = inject(ViewSwitchService);
  goToCustom() {
    this.viewSvc.go('custom');
  }
}
