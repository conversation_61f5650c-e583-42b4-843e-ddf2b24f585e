import { Component, OnInit } from '@angular/core';
import { Form<PERSON><PERSON>er, FormGroup, FormsModule } from '@angular/forms';
import { ActivatedRoute, Router, RouterLink } from '@angular/router';
import { MetaService } from '@src/app/modules/core/service/meta.service';
import { TranslationService } from '@src/app/modules/core/service/translation.service';
import { AdsRoutes, StripAdsType } from '@src/app/shared/models/lookup.model';
import { AdsService } from '@src/app/shared/services/ads.service';
import { debounceTime, map, Observable, Subject } from 'rxjs';
import { SortBy } from 'src/app/modules/core/enums';
import { BrowserService } from 'src/app/modules/core/service/browser.service';
import { Pagination } from 'src/app/shared/models/base.response.model';
import { ListingStatusType } from 'src/app/shared/models/listing-status-type';
import { ListingDetails } from 'src/app/shared/models/listing.model';
import { GetOfferFilters, OfferFilters, OfferStatus, OffersView, OfferTabsEnum } from 'src/app/shared/models/offer.model';
import { AuthService } from 'src/app/shared/services/auth.service';
import { DeviceDetectionService } from 'src/app/shared/services/device-detection.service';
import { OfferService } from 'src/app/shared/services/offer.service';
import { FormUtils } from 'src/utils/form-utils';
import { NgIf, NgClass, NgFor, AsyncPipe } from '@angular/common';
import { DialogModule } from 'primeng/dialog';
import { SideOfferFilterComponent } from '../../components/side-offer-filter/side-offer-filter.component';
import { OfferTopFilterBoxComponent } from '../../components/offer-top-filter-box/offer-top-filter-box.component';
import { DropdownModule } from 'primeng/dropdown';
import { PrimeTemplate } from 'primeng/api';
import { OfferCardComponent } from '../../components/offer-card/offer-card.component';
import { NoResultComponent } from '../../../../shared/components/no-result/no-result.component';
import { PaginatorModule } from 'primeng/paginator';
import { NtranslatePipe } from '../../../../shared/pipes/ntranslate.pipe';

@Component({
    selector: 'app-offers-list',
    templateUrl: './offers-list.component.html',
    styleUrls: ['./offers-list.component.scss'],
    standalone: true,
    imports: [NgIf, DialogModule, SideOfferFilterComponent, OfferTopFilterBoxComponent, NgClass, RouterLink, DropdownModule, FormsModule, PrimeTemplate, NgFor, OfferCardComponent, NoResultComponent, PaginatorModule, AsyncPipe, NtranslatePipe]
})
export class OffersListComponent implements OnInit {

  OfferTabsEnum = OfferTabsEnum;

  types: ListingStatusType[];

  offerStatus = OfferStatus;

  list$: Observable<OffersView[]>;
  search: string;
  filters: GetOfferFilters = {
    search: '',
  };
  pagination: Pagination = {
    currentPage: 1,
    pageSize: 12,
    totalItems: 0,
    totalPages: 0,
  };

  userParams: any = {
    sortBy: 'date',
    isSortAscending: false,
    name: '',
  };

  currentStatus = OfferTabsEnum.Received;


  offer: ListingDetails;

  sortBy: SortBy[] = [
    { value: 'date', viewValue: 'Newest', isSortAscending: false },
    { value: 'price', viewValue: 'HighPrice', isSortAscending: false },
    { value: 'price', viewValue: 'LowPrice', isSortAscending: true },
  ];
  selectedSortBy?: SortBy;

  filterVisible: boolean = false;

  filter: OfferFilters = {
  };


  filterResults$ = new Subject();

  form: FormGroup;

  constructor(

    private _offersService: OfferService,
    protected _authService: AuthService,
    private activatedRoute: ActivatedRoute,
    public deviceDetection: DeviceDetectionService,
    private fb: FormBuilder,
    private router: Router,
    private browser: BrowserService,
    private metaService: MetaService,
    private translateService: TranslationService,
    private adsService: AdsService,



  ) {
    this.adsService.setAdsRoute(AdsRoutes.MyOffers);
    this.adsService.setAdsStripType(StripAdsType.Offers);

  }

  ngOnInit(): void {

    this.metaService.set({
      title: this.translateService.instant('Offers'),
    });

    this.filterResults$.pipe(
      debounceTime(500),
    )
      .subscribe(() => {
        this.getList();
      });

    this.activatedRoute.data.subscribe(res => {
      this.currentStatus = res['type'];
      //this.getList();
    });

    this.activatedRoute.data.subscribe(res => {
      if (res['type']) {
        this.currentStatus = res['type'];
      }
      this.filterResults$.next(true);
    });

    this.activatedRoute.queryParams.subscribe(params => {


      if (params['pageNumber']) {

        for (let obj in params) {

          if (obj == 'selectedProperties') {
            this.filter[obj] = JSON.parse(atob(params[obj]));
          } else {
            this.filter[obj] = obj == 'conditions' ? +params[obj] > 0 ? [+params[obj]] : [] : FormUtils.parseValue(params[obj]);
          }


        }

        this.pagination.currentPage = +params['pageNumber'];

        setTimeout(() => {
          this.browser.scrollTo({
            behavior: 'smooth',
            top: 0,
          });
        }, 0);

      } else {
        this.filter = {
          ...this.filter,
          sortBy: this.userParams.sortBy,
          isSortAscending: this.userParams.isSortAscending
        };
      }

      this.selectedSortBy = this.sortBy.find(item => item.value == this.filter.sortBy && item.isSortAscending == this.filter.isSortAscending);

      this.filterResults$.next(true);

    });

  }

  onShowFilter(value) {
    this.filterVisible = value;
  }

  onSortingChangedCtrl($event) {
    this.selectedSortBy = $event;
    this.onSortingChanged();

  }

  onSortingChanged() {


    this.filter = {
      ...this.filter,
      sortBy: this.selectedSortBy?.value ?? this.userParams.sortBy,
      isSortAscending: this.selectedSortBy?.isSortAscending ?? this.userParams.isSortAscending
    };

    if (this.pagination.currentPage! > 1) {
      this.filter.pageNumber = 1;
      this.changeRoute([]);
    } else {
      this.changeRoute([]);
    }


  }

  onSubmitFilter(e) {
    this.filterVisible = false;
    if (e.clear) {
      this.pagination.currentPage = 1;
    }

    this.filter = {
      ...this.filter,
      pageNumber: this.pagination.currentPage,
      pageSize: this.pagination.pageSize,
      priceFrom: e.priceFrom,
      priceTo: e.priceTo,
      status: e.status,
      dateFrom: e.dateFrom,
      StatusIds: e.StatusIds,
      dateTo: e.dateTo,
      sortBy: this.selectedSortBy?.value ?? this.userParams.sortBy,
      isSortAscending: this.selectedSortBy?.isSortAscending ?? this.userParams.isSortAscending,
    };



    if (this.pagination.currentPage! > 1) {
      this.filter.pageNumber = 1;

    }
    this.changeRoute([]);


  }

  changeRoute(url) {
    setTimeout(() => {
      this.browser.scrollTo({
        behavior: 'smooth',
        top: 0,
      });
    }, 0);
    this.router.navigate(url, { queryParams: this.filter });
  }

  onChangeType(e) {
    if (e.value) {
      this.router.navigate([e.value.url]);
    }
  }



  getList() {
    this.filter = {
      ...this.filter,
      pageNumber: this.pagination.currentPage,
      pageSize: this.pagination.pageSize,
      sortBy: this.filter.sortBy ?? this.userParams.sortBy,
      isSortAscending: this.filter.isSortAscending ?? this.userParams.isSortAscending,
      tabNo: this.currentStatus
    };


    this.list$ = this._offersService.listOffers(this.filter).pipe(map(res => res.body.data), map(res => {
      this.pagination.totalItems = res!.totalCount;
      this.pagination.totalPages = res!.totalPages;

      return res!.items.filter(item => item.fromUser != null && item.toUser != null);
    }));
  }

  onSearch(value) {
    this.filter.search = value;
    this.gotoSearch();
  }



  gotoSearch() {
    this.pagination.currentPage = 1;
    this.filter = {
      ...this.filter,
      pageNumber: this.pagination.currentPage,
    };
    this.changeRoute([]);
  }

  pageChanged(event: any): void {
    this.filter.pageNumber = (parseInt(event.page) + 1);
    this.changeRoute([]);
  }

}
