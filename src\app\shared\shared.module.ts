import { CommonModule } from '@angular/common';
import { CUSTOM_ELEMENTS_SCHEMA, NgModule } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { AuthImgSideComponent } from '@shared/components/auth-img-side/auth-img-side.component';
import { AuthSocialComponent } from '@shared/components/auth-social/auth-social.component';
import { ChooseUrCityComponent } from '@shared/components/choose-ur-city/choose-ur-city.component';
import { CustomizeInterestsComponent } from '@shared/components/customize-interests/customize-interests.component';
import { EditPhoneModalComponent } from '@shared/components/edit-phone-modal/edit-phone-modal.component';
import { EnterPhoneNumberComponent } from '@shared/components/enter-phone-number/enter-phone-number.component';
import { GrayBtnComponent } from '@shared/components/gray-btn/gray-btn.component';
import { PasswordInputComponent } from '@shared/components/inputs/password-input/password-input.component';
import { PhoneInputComponent } from '@shared/components/inputs/phone-input/phone-input.component';
import { ItemCardRowComponent } from '@shared/components/item-card-row/item-card-row.component';
import { ItemCardComponent } from '@shared/components/item-card/item-card.component';
import { LanguageButtonComponent } from '@shared/components/language-button/language-button.component';
import { LoaderSpinnerComponent } from '@shared/components/loader-spinner/loader-spinner.component';
import { PasswordUpdatedModalComponent } from '@shared/components/password-updated-modal/password-updated-modal.component';
import { PrimaryBtnComponent } from '@shared/components/primary-btn/primary-btn.component';
import { ProgressBarComponent } from '@shared/components/progress-bar/progress-bar.component';
import { ProgressHeadComponent } from '@shared/components/progress-head/progress-head.component';
import { SecondaryBtnComponent } from '@shared/components/secondary-btn/secondary-btn.component';
import { SideFilterComponent } from '@shared/components/side-filter/side-filter.component';
import { SuccessStepComponent } from '@shared/components/success-step/success-step.component';
import { VerificationNumberComponent } from '@shared/components/verification-number/verification-number.component';
import { YouMayLikeComponent } from '@shared/components/you-may-like/you-may-like.component';
import { NCurrencyPipe } from '@shared/pipes/ncurrency.pipe';
import { NtranslatePipe } from '@shared/pipes/ntranslate.pipe';
import { NgxOtpInputComponent } from 'ngx-otp-input';
import { ButtonModule } from 'primeng/button';
import { ChooseCityComponent } from 'src/app/authentication/user-registeration/choose-city/choose-city.component';
import { VerificationComponent } from 'src/app/authentication/user-registeration/verification/verification.component';
import { TopLeftMenuComponent } from 'src/app/layout/top-nav/components/top-left-menu/top-left-menu.component';
import { TopRightMenuComponent } from 'src/app/layout/top-nav/components/top-right-menu/top-right-menu.component';
import { SvgIconsComponent } from './components/svg-icons/svg-icons.component';
import { SharedBtnComponent } from './components/shared-btn/shared-btn.component';
import { CheckBoxFormControlerComponent } from './components/check-box-form-controler/check-box-form-controler.component';

@NgModule({
    imports: [
        CommonModule,
        FormsModule,
        NgxOtpInputComponent,
        ReactiveFormsModule,
        PasswordInputComponent,
        PhoneInputComponent,
        PrimaryBtnComponent,
        LanguageButtonComponent,
        ItemCardComponent,
        ItemCardRowComponent,
        SideFilterComponent,
        SecondaryBtnComponent,
        GrayBtnComponent,
        YouMayLikeComponent,
        TopLeftMenuComponent,
        TopRightMenuComponent,
        NtranslatePipe,
        NCurrencyPipe,
        ButtonModule,
        LoaderSpinnerComponent,
        AuthImgSideComponent,
        AuthSocialComponent,
        ProgressBarComponent,
        EditPhoneModalComponent,
        SuccessStepComponent,
        VerificationNumberComponent,
        EnterPhoneNumberComponent,
        ChooseUrCityComponent,
        CustomizeInterestsComponent,
        PasswordUpdatedModalComponent,
        ChooseCityComponent,
        ProgressHeadComponent,
        VerificationComponent,
        SvgIconsComponent,
        SharedBtnComponent,
        CheckBoxFormControlerComponent
    ],
    schemas: [CUSTOM_ELEMENTS_SCHEMA],
    exports: [
        SecondaryBtnComponent,
        ItemCardComponent,
        ItemCardRowComponent,
        LanguageButtonComponent,
        ProgressHeadComponent,
        AuthImgSideComponent,
        GrayBtnComponent,
        AuthSocialComponent,
        ProgressBarComponent,
        EditPhoneModalComponent,
        SuccessStepComponent,
        VerificationNumberComponent,
        EnterPhoneNumberComponent,
        ChooseUrCityComponent,
        CustomizeInterestsComponent,
        PasswordUpdatedModalComponent,
        PasswordInputComponent,
        PhoneInputComponent,
        PrimaryBtnComponent,
        SideFilterComponent,
        YouMayLikeComponent,
        TopLeftMenuComponent,
        TopRightMenuComponent,
        NtranslatePipe,
        NCurrencyPipe,
        ButtonModule,
        LoaderSpinnerComponent,
        SvgIconsComponent,
        SharedBtnComponent,
        CheckBoxFormControlerComponent
    ],
})
export class SharedModule { }
