.page-container {
    line-height: 2;
    font-size: 0.8rem;

    h1 {
        margin-top: 40px;
        font-size: 2em;
    }

    h2 {
        font-size: 1.5em;
    }

    h3 {
        font-size: 1.2em;
    }



    .page_image {
        max-width: 100%;
        margin-top: 50px;
    }
}

.about-wrapper {
    margin: 0;
    line-height: 1.6;
    font-size: 1rem;

    &.ltr-page {
        direction: ltr;
    }

    h1,
    h2,
    h3 {
        font-weight: bold;
    }
}


.about-wrapper header {
    border-bottom: 1px solid #ddd;
    text-align: center;
    padding: 2rem 1rem;
}

.about-wrapper header h1 {
    margin: 0;
    font-size: 2.5rem;
    color: #2a2a2a;
}

.about-wrapper header p {
    font-size: 1.2rem;
    margin-top: 0.5rem;
    color: #555;
}

.about-wrapper section {
    max-width: 900px;
    margin: 2rem auto;
    padding: 0 1rem;
}

.about-wrapper .features {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
    margin: 1.5rem 0px;
}

.about-wrapper .feature {
    background: #ffffff;
    border: 1px solid #ddd;
    border-radius: 12px;
    padding: 1.5rem;
    transition: box-shadow 0.3s ease;
}

.about-wrapper .feature:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.about-wrapper .feature h3 {
    margin-top: 0;
    color: #111;
}

.about-wrapper footer {
    text-align: center;
    font-size: 0.9rem;
    padding: 1rem;
    color: #666;
    border-top: 1px solid #ddd;
    margin-top: 2rem;
}