import { Component } from '@angular/core';
import { SharedBtnComponent } from "@src/app/shared/components/shared-btn/shared-btn.component";
import { PackageDetailsComponent } from "../package-details/package-details.component";
import { ViewSwitchService } from '../../services/view-switch.service';
import { PackagesService } from '../../services/packages.service';
import { DynamicDialogRef } from "primeng/dynamicdialog";
import { DynamicHeaderComponent } from "@src/app/shared/components/dynamic-header/dynamic-header.component";

@Component({
  selector: 'app-subscription-package',
  standalone: true,
  imports: [SharedBtnComponent, PackageDetailsComponent , DynamicHeaderComponent],
  templateUrl: './subscription-package.component.html',
  styleUrl: './subscription-package.component.scss'
})
export class SubscriptionPackageComponent {

   constructor(private view: ViewSwitchService,private packagesService: PackagesService) {}

  goback(){
      this.view.go("business");
    }
  get selectedPlan() {
      return this.packagesService.selectedPlan();
    }

}
