<div class="profile-header" *ngIf="profile$ | async as profile">

  <app-profile-image [user]="profile" />

  <div class="profile-info">
    <h2 class="name">
      {{ profile.businessName ? profile.businessName : profile.firstName + profile.lastName }}
      <app-svg-icons name="icon-check" width="22px" height="22px"></app-svg-icons>
    </h2>

    <div (click)="goTo('ratings')">
      <app-stars-widget [ratingValue]="+profile.rating" [numberOfRating]="+profile.ratingCount" />
    </div>

    <div class="profile-type" *ngIf="!hideSetting || profile.businessName">
      <span class="premium" *ngIf="profile.businessName">حساب باقة مميز</span>
      <button *ngIf="!hideSetting ;else packageBtn" (click)="goTo('manage-account')" class="manage-btn">إدارة
        الحساب</button>

      <ng-template #packageBtn>
        <button (click)="openPackages()" class="manage-btn">إدارة الباقة</button>
      </ng-template>

    </div>
  </div>
</div>