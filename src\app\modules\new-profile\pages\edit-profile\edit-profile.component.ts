import { Component } from "@angular/core";
import { Router } from "@angular/router";
import { DynamicHeaderComponent } from "@src/app/shared/components/dynamic-header/dynamic-header.component";
import { ArrowBtnComponent } from "@src/app/shared/components/arrow-btn/arrow-btn.component";
import { ProfileModel } from "@src/app/shared/models/profile.model";
import { ProfileService } from "@src/app/shared/services/profile.service";
import { NgIf, NgClass } from "@angular/common";
import { SellerProfileImageComponent } from "@src/app/modules/seller/components/seller-profile-image/seller-profile-image.component";
import { BusinessProfileImageComponent } from "@src/app/modules/seller/components/business-profile-image/business-profile-image.component";
import { HeicConversionService } from '@src/app/core/services/heic.service';
import { BrowserService } from '@src/app/modules/core/service/browser.service';
import { AlertHandlerService } from '@src/app/modules/core/alerts/alert-handler.service';
import { TranslationService } from '@src/app/modules/core/service/translation.service';
import { AuthService } from '@src/app/shared/services/auth.service';
import { FormUtils } from '@src/utils/form-utils';
import { SvgIconsComponent } from "@src/app/shared/components/svg-icons/svg-icons.component";
import { ProfileCoverComponent } from "@src/app/modules/profile/components/profile-cover/profile-cover.component";

@Component({
  selector: "app-edit-profile",
  standalone: true,
  imports: [
    NgIf,
    NgClass,
    DynamicHeaderComponent,
    ArrowBtnComponent,
    SellerProfileImageComponent,
    BusinessProfileImageComponent,
    SvgIconsComponent,
    ProfileCoverComponent
],
  templateUrl: "./edit-profile.component.html",
  styleUrl: "./edit-profile.component.scss",
})
export class EditProfileComponent {
  user: ProfileModel | null = null;
  loading = false;
  fileToUpload: { name: string; blob: Blob } | null = null;
  maxWidth = 1000;

  constructor(
    private router: Router,
    private profileService: ProfileService,
    private hcs: HeicConversionService,
    private browser: BrowserService,
    private alertService: AlertHandlerService,
    private translateService: TranslationService,
    private authService: AuthService
  ) {}

  ngOnInit(): void {
    this.loadUser();
  }

  goback() {
    this.router.navigate([`/authentication/new-profile/manage-account`]);
  }
  onCoverUpdated() {
      this.loadUser();
    }
  loadUser() {
    this.loading = true;
    this.profileService.getUserProfile().subscribe({
      next: (res: ProfileModel) => {
        this.user = res;
        this.loading = false;
      },
      error: () => { this.loading = false; }
    });
  }

  get isBusiness(): boolean {
    const u = this.user;
    return !!(u && u.account && u.account.accountType === 'business');
  }

  async handleFileInput($event: any) {
    if (!this.browser.isBrowser() || !this.user) return;

    const files: FileList = $event.target.files;
    if (!files || !files.length) return;

    const fileImg = files[0];

    if (!FormUtils.validImageFile(fileImg)) {
      this.alertService.warn({ message: this.translateService.instant('Invalid_Extension') });
      return;
    }

    let sourceFile: File = fileImg;
    const isHeic = FormUtils.validHicFile(fileImg);
    if (isHeic) {
      sourceFile = await this.hcs.convertIfHeic(fileImg);
    }

    this.resizeAndConvertImage(sourceFile, async (event, blob) => {
      this.fileToUpload = { name: sourceFile.name, blob };
      this.user!.imageURL = event.target.result;
      await this.saveImage();
    });
  }

  private async saveImage() {
    if (!this.fileToUpload || !this.user) return;

    this.loading = true;
    const uploadData = new FormData();
    uploadData.append('userprofile', this.fileToUpload.blob, this.fileToUpload.name);
    uploadData.append('id', this.user.userID!.toString());

    this.profileService.uploadImgs(uploadData).subscribe({
      next: (y) => {
        const newUrl = y?.body?.imageURL!;
        const authUser = { ...this.authService.userValue, userImage: newUrl };
        this.user = { ...this.user!, userImage: newUrl };
        this.loading = false;
        this.authService.sendUserChanged(authUser);
        this.loadUser();
      },
      error: () => { this.loading = false; }
    });
  }

  private resizeAndConvertImage(file: File, callback: (e: any, blob: Blob) => void) {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = (event: any) => {
      const img = new Image();
      img.src = event.target.result;
      img.onload = () => {
        const canvas = document.createElement('canvas');
        const scaleSize = this.maxWidth / img.width;
        canvas.width = this.maxWidth;
        canvas.height = img.height * scaleSize;

        const ctx = canvas.getContext('2d')!;
        ctx.drawImage(img, 0, 0, canvas.width, canvas.height);

        ((ee) => {
          canvas.toBlob((blob) => callback(ee, blob!), 'image/jpeg');
        })(event);
      };
    };
  }
}
