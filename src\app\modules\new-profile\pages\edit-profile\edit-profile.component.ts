import { Component } from '@angular/core';
import { Router } from '@angular/router';
import { DynamicHeaderComponent } from "@src/app/shared/components/dynamic-header/dynamic-header.component";
import { ArrowBtnComponent } from "@src/app/shared/components/arrow-btn/arrow-btn.component";
import { ProfileModel } from '@src/app/shared/models/profile.model';
import { ProfileService } from '@src/app/shared/services/profile.service';
import { AuthService } from '@src/app/shared/services/auth.service';

@Component({
  selector: 'app-edit-profile',
  standalone: true,
  imports: [DynamicHeaderComponent, ArrowBtnComponent],
  templateUrl: './edit-profile.component.html',
  styleUrl: './edit-profile.component.scss'
})
export class EditProfileComponent {
   profile: ProfileModel | null = null;

  constructor(
    private router: Router,
    private profileService: ProfileService,
    private auth: AuthService
  ) { }

  ngOnInit(): void {
    const current = this.auth.userValue;
    if (current) {
      this.profile = {
        ...current,
        userID: current.userID ?? current.id ?? current.userId
      } as ProfileModel;
    }

    this.loadProfileFromServer();
  }

  private loadProfileFromServer(): void {
    this.profileService. getUserProfile().subscribe({
      next: (p) => { this.profile = p; },
      error: () => {  }
    });
  }

  refreshProfile(): void {
    this.loadProfileFromServer();
  }

  goback() {
    this.router.navigate([`/authentication/new-profile/manage-account`]);
  }
}
