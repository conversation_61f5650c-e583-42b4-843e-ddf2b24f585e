import { CommonModule } from '@angular/common';
import { Component, ElementRef, EventEmitter, Input, OnDestroy, OnInit, Output, computed, inject, signal } from '@angular/core';
import { BrowserService } from '@src/app/modules/core/service/browser.service';
import { ConversionEvents } from '../../constants/conversion-events';
import { AdsSectionDTO } from '../../models/lookup.model';
import { AdsService } from '../../services/ads.service';
import { CommonService } from '../../services/common.service';
import { CustomMpuComponent } from '../custom-mpu/custom-mpu.component';
import { RegularMpuComponent } from '../regular-mpu/regular-mpu.component';

@Component({
  selector: 'app-mpu-container',
  standalone: true,
  imports: [CommonModule, CustomMpuComponent, RegularMpuComponent],
  templateUrl: './mpu-container.component.html',
  styleUrl: './mpu-container.component.scss'
})
export class MpuContainerComponent implements OnInit, OnD<PERSON>roy {

  adsContainer = signal<AdsSectionDTO | null>(null);

  hideAds = signal(false);

  browser = inject(BrowserService);

  commonservice = inject(CommonService);

  adsService = inject(AdsService);

  private elementRef: ElementRef = inject(ElementRef);

  private observer!: any;
  options = {
    root: null,
    rootMargin: '0px',
    threshold: 0.3
  };


  @Input()
  set adsContainerInput(value: AdsSectionDTO) {
    this.adsContainer.set(value);
  }

  @Input() handleEvents: boolean = true;
  @Input() keyName!: string;

  @Output() shouldHideAds = new EventEmitter<boolean>();

  isCustomMpu = computed(() => {
    const container = this.adsContainer();
    return container?.locationName?.includes('CustomMpu') ?? false;
  });

  isRegularMpu = computed(() => {
    const container = this.adsContainer();
    return container?.locationName?.includes('RegularMpu') ?? false;
  });

  hasValidAds = computed(() => {
    const container = this.adsContainer();
    return !!(container?.ads && container.ads.length > 0);
  });

  ads = computed(() => {
    return this.adsContainer()?.ads ?? [];
  });

  ngOnInit(): void {
    const shouldShow = this.shouldShowCarouselAd();

    if (shouldShow && this.handleEvents) {
      this.setupIntersectionObserver();
    }

    if (this.keyName?.startsWith('carousel_ads_')) {
      this.shouldHideAds.emit(!shouldShow);
    }
  }

  shouldShowCarouselAd(): boolean {
    const ad = this.adsContainer();
    if (!ad) return false;

    const maxDisplaysPerSession = ad.maxDisplaysPerSession || 0;

    if (maxDisplaysPerSession > 0) {
      const currentViewCount = this.getAdsImpressionCount();

      if (currentViewCount >= maxDisplaysPerSession) {
        this.hideAds.set(true);
        return false;
      }
    }

    return true;
  }

  setupIntersectionObserver(): void {
    if (!this.browser.isBrowser() || this.observer) {
      return;
    }

    this.observer = new IntersectionObserver(entries => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          this.sendEvent();
          this.observer.unobserve(this.elementRef.nativeElement);
        }
      });
    }, this.options);

    this.observer.observe(this.elementRef.nativeElement);
  }

  sendEvent() {
    if (!this.browser.isBrowser()) {
      return;
    }

    this.incrementAdsImpressionCount();

    this.commonservice.pushDataLayer({
      event: ConversionEvents.ADS_IMPRESSION,
      ads_id: this.adsContainer()?.ads[0]?.id || '',
      ads_sponsore_id: this.adsContainer()?.ads[0]?.sponsorId,
      ads_location: this.adsContainer()?.locationName || '',
      ads_key: this.keyName || ''
    });
  }

  onAdClick() {
    if (!this.browser.isBrowser()) {
      return;
    }
    this.commonservice.pushDataLayer({
      event: ConversionEvents.ADS_CLICK,
      ads_id: this.adsContainer()?.ads[0]?.id || '',
      ads_sponsore_id: this.adsContainer()?.ads[0]?.sponsorId,
      ads_location: this.adsContainer()?.locationName || '',
      ads_key: this.keyName || ''
    });

    this.adsService.adsClick(this.adsContainer()?.ads[0]?.sponsorId).subscribe();
  }

  getAdsImpressionCount(): number {

    if (!this.browser.isBrowser()) {
      return 0;
    }

    const storageKey = `${this.keyName}_ads_impression_count`;
    const currentCount = parseInt(sessionStorage.getItem(storageKey) || '0', 10);
    return currentCount;

  }

  incrementAdsImpressionCount(): void {
    if (!this.browser.isBrowser()) {
      return;
    }


    const storageKey = `${this.keyName}_ads_impression_count`;
    const currentCount = parseInt(sessionStorage.getItem(storageKey) || '0', 10);
    const newCount = currentCount + 1;
    sessionStorage.setItem(storageKey, newCount.toString());
  }



  ngOnDestroy() {
    if (this.browser.isBrowser()) {
      if (this.observer) {
        this.observer.disconnect();
      }
    }
  }


}
