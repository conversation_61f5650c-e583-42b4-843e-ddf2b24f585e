import { AdsAndAddOnsService, AdsStage } from '@src/app/modules/ads-and-add-ons/services/ads-and-add-ons.service';
import { Component } from '@angular/core';
import { PointsStatusWidgetComponent } from "@src/app/shared/components/points-status-widget/points-status-widget.component";
import { SharedBtnComponent } from '@src/app/shared/components/shared-btn/shared-btn.component';
import { NtranslatePipe } from '@src/app/shared/pipes/ntranslate.pipe';
import { SvgIconsComponent } from '@src/app/shared/components/svg-icons/svg-icons.component';
import { NewRangeInputComponent } from '@src/app/shared/components/new-range-input/new-range-input.component';

@Component({
  selector: 'app-extend-duration-component',
  standalone: true,
  imports: [PointsStatusWidgetComponent, SharedBtnComponent, NtranslatePipe, SvgIconsComponent, NewRangeInputComponent],
  templateUrl: './extend-duration-component.component.html',
  styleUrl: './extend-duration-component.component.scss'
})
export class ExtendDurationComponentComponent {

  dayNumber = 5
  AdsStage = AdsStage
  constructor(private adsAndAddOnsService: AdsAndAddOnsService) { }
  onAdd() {
    this.adsAndAddOnsService.goTo(AdsStage.Success);
  }
}
