@import 'variables';

.chart-card {
    display: flex;
    flex-direction: column;
    margin-bottom: 2rem;
}

.card-title {
    font-weight: bold;
    font-size: 16px;
    margin-bottom: 6px;
    text-align: start;
    color: $text-color;
}

/* overlay */
.chart-loader {
    position: absolute;
    inset: 0;
    display: grid;
    place-items: center;
    background: rgba(255, 255, 255, 0.6);
    /* tweak or remove if you prefer */
    z-index: 2;
}

/* pure CSS spinner */
.spinner {
    width: 22px;
    height: 22px;
    border: 3px solid #e6e6e6;
    border-top-color: $primary;
    border-radius: 50%;
    animation: spin .7s linear infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}