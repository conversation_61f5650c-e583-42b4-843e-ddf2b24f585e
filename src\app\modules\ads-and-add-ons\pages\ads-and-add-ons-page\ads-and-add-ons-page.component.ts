import { AdsAndAddOnsSheetCycleComponent } from './../../ads-and-add-ons-sheet-cycle/ads-and-add-ons-sheet-cycle.component';
import { AlertHandlerService } from './../../../core/alerts/alert-handler.service';
import { Component } from '@angular/core';
import { StatsBlockComponent } from '../../components/stats-block/stats-block.component';
import { CommonModule } from '@angular/common';
import { ListingCardRowComponent } from '@src/app/shared/components/listing-card-row/listing-card-row.component';
import { NtranslatePipe } from '@src/app/shared/pipes/ntranslate.pipe';
import { DynamicHeaderComponent } from '@src/app/shared/components/dynamic-header/dynamic-header.component';
import { FilterPanelComponent } from '../../components/filter-panel/filter-panel.component';
import { Router } from '@angular/router';
import { SvgIconsComponent } from '@src/app/shared/components/svg-icons/svg-icons.component';
@Component({
  selector: 'app-ads-and-add-ons-page',
  standalone: true,
  imports: [StatsBlockComponent,
    CommonModule,
    ListingCardRowComponent,
    NtranslatePipe,
    DynamicHeaderComponent,
    SvgIconsComponent],
  templateUrl: './ads-and-add-ons-page.component.html',
  styleUrl: './ads-and-add-ons-page.component.scss'
})
export class AdsAndAddOnsPageComponent {

  selectedMode: boolean = false;

  dashboardStats = [
    { label: "إجمالي المكالمات", value: 231, icon: 'phone-icon' },
    { label: "إجمالي الرسائل", value: 231, icon: 'message-icon' },
    { label: "إجمالي العروض", value: 231, icon: 'eye-icon' },
    { label: "إجمالي المشاهدات", value: 231, icon: 'arrow-top-left' },
    { label: "إجمالي النقرات", value: 231, icon: 'tag-icon' },
  ];

  ads = [
    {
      id: 1,
      name: 'Redmi note 13 pro 4G',
      webpImageURL: '../../../../../../assets/images/image 4.jpg',
      callCount: 200,
      views: 32,
      appearanceCount: 103,
      tags: ['Top Featured'],
    },
    {
      id: 2,
      name: 'شقة للبيع متشطبة أساً',
      webpImageURL: '../../../../../../assets/images/image 4.jpg',
      callCount: 200,
      views: 32,
      appearanceCount: 103,
      tags: ['Top Featured'],
    },
    {
      id: 3,
      name: 'ريس هوندا هورنت ...200',
      webpImageURL: '../../../../../../assets/images/image 4.jpg',
      callCount: 200,
      views: 32,
      appearanceCount: 103,
      tags: ['Ad Bump', 'Top Featured'],
    },
  ];


  filtersValues = {
    sortOption: '',
    callRange: '',
    messageRange: '',
    title: 'اختار تاريخ الاحصائيات',
    selectedDateRange: [],
  }

  selectedAds = new Set<number>();

  constructor(private alertHandlerService: AlertHandlerService) { }



  toggleSelection(adId: number): void {
    if (!this.selectedMode) return
    this.selectedAds.has(adId)
      ? this.selectedAds.delete(adId)
      : this.selectedAds.add(adId);
  }

  isSelected(adId: number): boolean {
    return this.selectedAds.has(adId);
  }

  cancel() {
    this.selectedMode = false;
    this.selectedAds.clear();
  }

  addSelectedAds() {
    console.log('Selected Ads:', Array.from(this.selectedAds));

    if (this.selectedAds.size !== 0)
      this.alertHandlerService.DynamicDialogOpen<AdsAndAddOnsSheetCycleComponent>(AdsAndAddOnsSheetCycleComponent, {}, (callbackData: any) => {
        if (callbackData) {
          this.filtersValues = callbackData;
        }
      })
  }

  openFilter() {
    this.alertHandlerService.DynamicDialogOpen<FilterPanelComponent>(FilterPanelComponent, this.filtersValues, (callbackData: any) => {
      if (callbackData) {
        console.log('Filter applied:', callbackData);
        this.filtersValues = callbackData;
      }
    })

  }

  getActiveFilters(): { key: string; label: string | string[] }[] {
    const filters: { key: string; label: string | string[] }[] = [];

    if (this.filtersValues.sortOption) {
      filters.push({ key: 'sortOption', label: this.filtersValues.sortOption });
    }
    if (this.filtersValues.callRange) {
      filters.push({ key: 'callRange', label: this.filtersValues.callRange });
    }
    if (this.filtersValues.messageRange) {
      filters.push({ key: 'messageRange', label: this.filtersValues.messageRange });
    }

    if (this.filtersValues.selectedDateRange.length > 0) {
      filters.push({ key: 'messageRange', label: this.filtersValues.selectedDateRange });
    }

    return filters;
  }

  removeFilter(key: string, event: MouseEvent) {
    event.stopPropagation();
    if (key === 'selectedDateRange') {
      this.filtersValues[key] = [];
    } else {
      this.filtersValues[key] = null;
    }
  }

  clearAllFilters() {
    this.filtersValues = {
      sortOption: null,
      callRange: null,
      title: 'اختار تاريخ الاحصائيات',
      messageRange: null,
      selectedDateRange: []
    };
  }

}
