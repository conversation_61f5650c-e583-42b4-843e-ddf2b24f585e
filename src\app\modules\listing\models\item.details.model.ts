import { TagDTO } from "src/app/shared/models/lookup.model";

interface basicDetails {
  name: string,
  description: string,
  price: number,
  condition: any,
  paymentMethod: any,
  keywords?: TagDTO[],
  contact?: contact
}

interface contact {
  listingContactName: string;
  listingContactPhone: string;
}

export interface itemDetails {
  form: basicDetails,
  treadBy?: string

}

export interface itemImages {
  urls: any[],
  filesUploaded: any[];
  coverURL: any;
  deletedImages?: number[];
}