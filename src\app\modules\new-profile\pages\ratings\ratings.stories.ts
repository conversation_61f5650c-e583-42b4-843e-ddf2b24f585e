import type { Meta, StoryObj } from '@storybook/angular';

import { RatingsComponent } from './ratings.component';

const meta: Meta<RatingsComponent> = {
  title: 'Profile/Rating',
  component: RatingsComponent,
  tags: ['autodocs'],
  argTypes: {
  },
};

export default meta;
type Story = StoryObj<RatingsComponent>;

export const Primary: Story = {
  args: {
  },
};

export const Secondary: Story = {
  args: {
  },
};

export const Large: Story = {
  args: {
  },
};

export const Small: Story = {
  args: {
    size: 'small',
    label: 'Button',
  },
};
