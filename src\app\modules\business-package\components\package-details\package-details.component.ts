import { CommonModule } from "@angular/common";
import { Component, Input, ViewEncapsulation } from "@angular/core";
import { PackagePlan } from "../../models/packages.model";
import { PackagesService } from "../../services/packages.service";
import { SvgIconsComponent } from "@src/app/shared/components/svg-icons/svg-icons.component";
import { LongDatePipe } from "@src/app/shared/pipes/long-date.pipe";

@Component({
  selector: "app-package-details",
  standalone: true,
  imports: [CommonModule, SvgIconsComponent , LongDatePipe],
  templateUrl: "./package-details.component.html",
  styleUrl: "./package-details.component.scss",
  encapsulation: ViewEncapsulation.None,
})
export class PackageDetailsComponent {

  @Input() plan!: PackagePlan | null;

  constructor(public packagesService: PackagesService) {}
  get plans() {
    return this.packagesService.plans();
  }

}
