import { computed, Injectable, signal } from '@angular/core';
import { AdsAndAddOns } from '../models/ads-and-add-ons.model';

export enum AdsStage {
  Picker = 1,
  Details = 2,
  Invoice = 3,
  ExtendDuration = 4,
  Success = 5
}

@Injectable({ providedIn: 'root' })
export class AdsAndAddOnsService {
  titlesMap: Record<AdsStage, string> = {
    [AdsStage.Picker]: 'اضافة',
    [AdsStage.Details]: 'اضافة',
    [AdsStage.Invoice]: 'ملخص فاتورة الدفع',
    [AdsStage.ExtendDuration]: 'تمديد Top Featured Ad',
    [AdsStage.Success]: '',
  };

  private readonly minStage = AdsStage.Picker;
  private readonly maxStage = AdsStage.Success;
  private lastStage: AdsStage | null = null;

  // ---- Signals: stage, ads list, selected item
  private readonly _stage = signal<AdsStage>(AdsStage.Picker);

  private readonly _ads = signal<AdsAndAddOns[]>([
    {
      id: 1,
      title: '<PERSON> Bump',
      iconId: 'Ad Bump',
      description:
        'تدوير الإعلان وإظهاره بشكل بارز في الصفحة الرئيسية، صفحة الفئة، ونتائج البحث.',
      validityValue: 7,
      costValue: 200,
      subscribed: false,
    },
    {
      id: 2,
      title: 'Top Featured Ad',
      iconId: 'Top Featured Ad',
      description:
        'يتم عرض الإعلان مع شارة مميزة بشكل بارز في الصفحة الرئيسية، صفحة الفئة، ونتائج البحث.',
      validityValue: 7,
      costValue: 300,
      subscribed: true,
      daysLeft: '4',
    },
    {
      id: 3,
      title: 'Highlight Ad',
      iconId: 'Highlight Ad',
      description:
        'مثال: “سيارة اليوم” — إعلان مميز يتم تثبيته لمدة ٢٤ ساعة ويحتاج إلى موافقة يدوية.',
      validityValue: 24,
      costValue: 2000,
      subscribed: false,
    },
    {
      id: 4,
      title: 'Push Notification',
      iconId: 'Push Notification',
      description:
        'إرسال رسائل Push مخصصة لمستخدمين مستهدفين ضمن فئة معينة.',
      validityValue: 0,
      costValue: 5000,
      subscribed: false,
    },
  ]);

  private readonly _selectedAddon = signal<AdsAndAddOns | null>(null);

  // ---- Readonly selectors exposed to components
  stage = this._stage.asReadonly();
  ads = this._ads.asReadonly();
  selectedAddon = this._selectedAddon.asReadonly();

  // ---- Computed helpers
  isFirst = computed(() => this._stage() <= this.minStage);
  isLast = computed(() => this._stage() >= this.maxStage);
  title = computed(() => this.titlesMap[this._stage()]);

  totalAds = computed(() => this._ads().length);

  // ========= Stage controls =========
  nextStage() {
    this.lastStage = this._stage();
    this._stage.update(s => (s < this.maxStage ? (s + 1) as AdsStage : s));
  }

  backStage() {
    // if coming from last step or any step, go back one
    const current = this._stage();
    if (this.lastStage === null) {
      this._stage.set(
        (current - 1 < this.minStage ? this.minStage : (current - 1)) as AdsStage
      );
    } else {
      this._stage.set(this.lastStage);
    }
    this.lastStage = null;
  }

  goTo(stage: AdsStage) {
    this.lastStage = this._stage();
    const clamped = Math.max(this.minStage, Math.min(stage, this.maxStage)) as AdsStage;
    this._stage.set(clamped);
  }

  reset() {
    this._stage.set(this.minStage);
    this._selectedAddon.set(null);
  }

  // ========= Ads list setters =========
  setAds(list: AdsAndAddOns[]) {
    this._ads.set(list ?? []);
    // keep selection valid
    const sel = this._selectedAddon();
    if (sel && !this._ads().some(a => a.id === sel.id)) {
      this._selectedAddon.set(null);
    }
  }

  // ========= Selection controls =========
  selectById(id: number) {
    const found = this._ads().find(a => a.id === id) ?? null;
    this._selectedAddon.set(found);
  }

}