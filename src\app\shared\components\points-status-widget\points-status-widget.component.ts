import { CommonModule } from '@angular/common';
import { Component, Input } from '@angular/core';
import { SvgIconsComponent } from '../svg-icons/svg-icons.component';
import { SharedBtnComponent } from "../shared-btn/shared-btn.component";

@Component({
  selector: 'app-points-status-widget',
  standalone: true,
  imports: [CommonModule, SvgIconsComponent, SharedBtnComponent],
  templateUrl: './points-status-widget.component.html',
  styleUrl: './points-status-widget.component.scss'
})
export class PointsStatusWidgetComponent {

  @Input() current = 100;
  @Input() usedPoints = 22;
  @Input() pointsRequired;

  @Input() title = 'حالة نقاط بعد الاضافة';
  @Input() showDetials = false;
  @Input() expiresAt: Date | string;
  @Input() label: string | null = null;
  @Input() iconName: string | null = null;
  @Input() clickedMode: boolean = false

  get remainingAfterPurchase(): number {
    return (this.usedPoints + this.pointsRequired) - this.current;
  }

  get cannotBuy(): boolean {
    return this.current < (this.usedPoints + this.pointsRequired);
  }

  get percent(): number {
    const p = (this.usedPoints / Math.max(1, this.current)) * 100;
    return Math.max(0, Math.min(100, Math.round(p * 10) / 10));
  }

  get percentwithRequired(): number {
    const p = ((this.usedPoints + this.pointsRequired) / Math.max(1, this.current)) * 100;
    return Math.max(0, Math.min(100, Math.round(p * 10) / 10));
  }
}
