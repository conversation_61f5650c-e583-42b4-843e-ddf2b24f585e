.picker {
    background: #fff;
    width: 100%;
    max-width: 400px;
    box-sizing: border-box;

    &__header {
        display: flex;
        align-items: center;
        margin-bottom: 16px;

        .icon-btn {
            background: none;
            border: none;
            font-size: 18px;
            cursor: pointer;
            margin-right: 8px;
        }

        .title {
            font-size: 18px;
            font-weight: bold;
        }

        .subtitle {
            font-size: 14px;
            color: #555;
        }
    }

    &__section-title {
        font-weight: bold;
        margin-bottom: 12px;
        font-size: 16px;
    }
}

.promo-card {
    background: #f5f5f5;
    border-radius: 6px;
    padding: 12px;
    margin-bottom: 16px;

    .header {
        margin-bottom: 8px;

        .title {
            font-weight: 700;
            font-size: 16px;
            color: #722282;
        }
    }

    .desc {
        font-size: 14px;
        margin-bottom: 8px;
    }

    .meta {
        font-size: 14px;
        margin-bottom: 12px;

        .label {
            font-weight: bold;
        }
    }

    .read-more {
        background: none;
        border: none;
        padding: 0;
        font-size: 12px;
        color: #722282;
        cursor: pointer;
        margin-top: 8px;
    }
}

.cta {
    display: block;
    width: 100%;
    padding: 10px;
    background: linear-gradient(180deg, #8b3fa9, #722282);
    color: #fff;
    border: none;
    border-radius: 6px;
    font-weight: bold;
    cursor: pointer;
    margin-top: 8px;

    &.outline {
        background: transparent;
        color: #722282;
        border: 1px solid #722282;
    }
}

.auto-renew {
    background: rgba(238, 238, 238, 1);
    padding: 8px;
    border-radius: 6px;
    font-size: 12px;
    font-weight: 600;
    margin-bottom: 8px;

    span {
        color: rgba(124, 124, 124, 1)
    }
}

.addon-section {
    margin-bottom: 1rem;
}

.notice {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    color: #9e9e9e;
    font-size: 14px;
    margin-bottom: 2rem;
    flex: 1 1 auto;
    min-width: 0;
}

.notice__name {
    color: rgba(124, 124, 124, 1); // darker & bold for the item name
    font-weight: 700;

    /* Curly quotes around the name */
    &::before {
        content: "“";
    }

    &::after {
        content: "”";
    }
}