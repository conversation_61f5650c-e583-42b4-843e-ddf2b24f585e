@import "variables";

.image_placeholder {
    text-align: center;
    margin: 1rem 1rem;
}

.section {
    margin: 2rem 0rem;
}

.title {
    font-size: 16px;
    font-weight: bold;
    margin: 1rem 0rem;
}

.invoice-summary {
    background: #fff;
    border-radius: 10px;
    padding: 12px 14px;
    color: #2d3142;
    font-family: inherit;

    .row {
        display: grid;
        grid-template-columns: auto 1fr auto;
        align-items: baseline;
        column-gap: 10px;
        margin: 8px 0;

        .label {
            font-weight: 700;
            white-space: nowrap;
        }

        .value {
            white-space: nowrap;
            font-weight: 700;
        }

        .dots {
            border-bottom: 1px dotted #cfd2dc;
            height: 1px;
            align-self: center;
            translate: 0 -2px;
        }

        &.neg .value {
            color: #b71c1c;
        }
    }

    // .section-sep {
    //     margin: 1rem 0;
    //     color: #7222821A;
    //     border: 0;
    //     border-top: 1px solid #7222821A;
    // }

    .total .value,
    .total .label {
        color: #722282;
        font-weight: 800;
    }
}

hr {
    margin: 1rem 0;
    color: #7222821A;
    border: 0;
    border-top: 1px solid #7222821A;
}

.meta {
    display: flex;
    flex-direction: column;
    gap: 4px;

    .row {
        display: flex;
        gap: 4px;
        font-size: 14px;
        color: $text-color;

        .label {
            font-weight: 700;
            padding: 0px;
        }

        .value {
            font-weight: 400;
        }
    }
}

.schedule-form {
    display: flex;
    flex-direction: column;
}

:host ::ng-deep .p-calendar.purple-text {
    position: relative;
    width: 100%;
    margin-bottom: 1rem;
}

:host ::ng-deep .p-calendar.purple-text .p-inputtext {
    padding-inline-end: 2rem;
    font-size: 14px;
    color: rgba(114, 34, 130, 1);
}

:host ::ng-deep .p-calendar.purple-text .custom-icon {
    position: absolute;
    inset-inline-end: .5rem;
    top: 45%;
    right: 3%;
    transform: translateY(-50%);
    background: transparent;
    border: 0;
    padding: 0;
    display: inline-flex;
    align-items: center;
    cursor: pointer;
}

.image-margin {
    margin-top: -5px;
    margin-bottom: 12px;
}