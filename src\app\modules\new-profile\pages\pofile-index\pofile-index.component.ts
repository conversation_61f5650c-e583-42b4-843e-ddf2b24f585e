import { Component } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { SvgIconsComponent } from '@src/app/shared/components/svg-icons/svg-icons.component';
import { NewProfileHeaderComponent } from '../../components/new-profile-header/new-profile-header.component';
import { RouterModule } from '@angular/router';

@Component({
  selector: 'app-profile-index',
  standalone: true,
  imports: [FormsModule, ReactiveFormsModule, SvgIconsComponent, NewProfileHeaderComponent, RouterModule],
  templateUrl: './pofile-index.component.html',
  styleUrl: './pofile-index.component.scss'
})
export class PofileIndexComponent {
  percentage = 70

}
