<div class="edit-data-page">

  <div class="header">
    <app-dynamic-header [showCloseButton]="false" [title]="'تعديل البيانات'" (backButtonClicked)="goback()"></app-dynamic-header>
  </div>

  <div class="section">

    <h3>الشعار والغلاف</h3>
    <ng-container *ngIf="user as u">
      <app-profile-cover
        [user]="u"
        [isEditMode]="false"
        (onEdit)="onCoverUpdated()">
      </app-profile-cover>
    </ng-container>
    <label class="profile-img" [ngClass]="{ 'loading': loading, 'businessSize': isBusiness }">

      <app-seller-profile-image
        [seller]="user"
        *ngIf="!isBusiness; else businessProfile">
      </app-seller-profile-image>

      <ng-template #businessProfile>
        <app-business-profile-image [business]="user"></app-business-profile-image>
      </ng-template>

      <input class="img_uplaod"
            #Image
            type="file"
            accept="image/*, .heic, .heif"
            (change)="handleFileInput($event)" />

      <i class="edit-icon">
        <app-svg-icons name="edit-icon" width="9px" height="9px"></app-svg-icons>
      </i>
    </label>


  </div>

  <br>

  <div class="profile-sections">
    <app-arrow-btn [iconName]="'briefcase-icon'" [title]="'تعديل بيانات الشركة'"></app-arrow-btn>
    <app-arrow-btn [iconName]="'user-id'" [title]="'تعديل بيانات مسؤول التواصل'"></app-arrow-btn>
    <app-arrow-btn [iconName]="'key-icon'" [title]="'تغيير كلمة السر'"></app-arrow-btn>
  </div>

</div>
