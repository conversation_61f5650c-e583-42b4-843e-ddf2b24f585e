<ng-container [ngSwitch]="viewSvc.view()">
  <app-business-package *ngSwitchCase="'business'"></app-business-package>

  <app-subscription-package
    *ngSwitchCase="'subscription'"
  ></app-subscription-package>

  <app-package-plan *ngSwitchCase="'plan'"></app-package-plan>

  <app-custom-subscription
    *ngSwitchCase="'custom'"
    [titleText]="custom.titleText"
  >
  </app-custom-subscription>

  <app-cancel-confirmation
    *ngSwitchCase="'cancel'"
    [title]="cancelInfo.title"
    [description]="cancelInfo.description"
    [btnText]="cancelInfo.btnText"
  >
  </app-cancel-confirmation>
  
  <div *ngSwitchCase="'unsubscribe'">
    <app-cancel-confirmation
      [title]="cancelpackage.title"
      [description]="cancelpackage.description"
      [btnText]="cancelpackage.btnText"
    >
    </app-cancel-confirmation>
  </div>

  <div *ngSwitchDefault class="empty">لم يتم اختيار شاشة.</div>
</ng-container>
