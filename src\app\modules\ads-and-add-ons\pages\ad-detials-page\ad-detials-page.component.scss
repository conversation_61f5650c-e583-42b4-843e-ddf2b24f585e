@import "variables";
@import "mixins";

/* Page */
.dashboard-page {
    padding: 24px;
    background: $secondary;
    min-height: 100vh;
    color: $text-color;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.ad-item {

    &--business {
        display: flex;
        align-items: center;
        gap: 12px;
        padding: 8px 12px;
        border-radius: 3px;
    }

    /* child styles */
    .ad-thumb {
        width: 327px;
        height: 203px;
        border-radius:3px;
        object-fit: cover;
        margin-bottom: 1rem;
    }

    &--business .ad-thumb {
        width: 56px;
        height: 56px;
        border-radius: 3px;
        object-fit: cover;
        flex: 0 0 56px;
    }
}

.ad-body {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: 6px;
    min-width: 0; // allow text ellipsis
}

.ad-title {
    margin: 0;
    font-size: 16px;
    font-weight: 800;
    color: $text-color;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.ad-link {
    display: inline-flex;
    align-items: center;
    gap: 6px;
    color: $primary;
    font-size: 12px;
    font-weight: 800;
    text-decoration: none;

    &:hover {
        text-decoration: underline;
    }
}

/* Tags */
.tags {
    display: flex;
    gap: 6px;
    margin: 8px 0;
    flex-wrap: wrap;
}

.tag-label {
    background-color: rgba(rgba(250, 175, 64, 1), 0.10);
    color: rgba(250, 175, 64, 1); 
    padding: 3px 12px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 600;
    display: inline-flex;
    align-items: center;
    gap: 4px;
}

/* Optional: rotate the back icon for RTL automatically */
::ng-deep app-svg-icons[name="back-icon"] svg {
    transform: rotate(180deg);
}

.filterHeader {
    color: #722282;
    font-size: 14px;
    font-weight: 700;
}