<div class="header-summary">
    <div class="header-title">
        <button *ngIf="showBackButton" (click)="onBackButtonClick()" aria-label="Back" class="icon-button">
            <app-svg-icons name="back-icon" width="15px" height="15px"></app-svg-icons>
        </button>
        <span class="page-title">{{ title }}</span>
    </div>

    <ng-container *ngIf="filterSlot; else autoFilter">
        <ng-container *ngTemplateOutlet="filterSlot"></ng-container>
    </ng-container>

    <ng-template #autoFilter>
        <button *ngIf="showFilterButton;" class="icon-button" (click)="onFilterButtonClick()">
            <app-svg-icons name="filter-icon" width="15px" height="15px"></app-svg-icons>
        </button>

        <button *ngIf="showCloseButton && !showFilterButton" class="icon-button" (click)="onCloseButtonClick()">
            <app-svg-icons name="closeBtn" width="15px" height="15px"></app-svg-icons>
        </button>
    </ng-template>

</div>