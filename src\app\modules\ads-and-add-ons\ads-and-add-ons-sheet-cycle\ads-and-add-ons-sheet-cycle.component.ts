import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { AdAddonsPickerComponent } from '../components/ad-addons-picker/ad-addons-picker.component';
import { DynamicHeaderComponent } from '@src/app/shared/components/dynamic-header/dynamic-header.component';
import { AdsAndAddOnsService, AdsStage } from '../services/ads-and-add-ons.service';
import { PyamentInvoiceAdsSummaryComponent } from '../components/pyament-invoice-ads-summary/pyament-invoice-ads-summary.component';
import { ExtendDurationComponentComponent } from "../components/extend-duration-component/extend-duration-component.component";
import { SuccessPopupAdAddonsComponent } from "../components/success-popup-ad-addons/success-popup-ad-addons.component";

@Component({
  selector: 'app-ads-and-add-ons-sheet-cycle',
  standalone: true,
  imports: [CommonModule,
    AdAddonsPickerComponent,
    DynamicHeaderComponent,
    PyamentInvoiceAdsSummaryComponent,
    ExtendDurationComponentComponent,
    SuccessPopupAdAddonsComponent],

  templateUrl: './ads-and-add-ons-sheet-cycle.component.html',
  styleUrl: './ads-and-add-ons-sheet-cycle.component.scss'
})
export class AdsAndAddOnsSheetCycleComponent {

  AdsStage = AdsStage;

  constructor(public svc: AdsAndAddOnsService) { }

}
