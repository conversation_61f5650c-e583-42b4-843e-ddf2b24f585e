import { Meta, StoryObj, moduleMetadata } from "@storybook/angular";
import { PackagePlanComponent } from "./package-plan.component";
import { PackagesService } from "../../services/packages.service";
import { ViewSwitchService } from "../../services/view-switch.service";
import { PackagePlan } from "../../models/packages.model";


const mockPlans: PackagePlan[] = [
  {
    id: 1,
    name: "خطة المبتدئين (Starter Plan)",
    currency: "EGP",
    price: 3840,
    labelTag: "الأكثر مبيعاً",
    renewableLabel: "It_will_be_renewed_automatically_after",
    htmlDescription:
      '<div class="package-details"><div class="item"><span class="label"><span class="dot-icon"></span> الصلاحية </span><span class="value">: 30 يوم</span></div><div class="item"><span class="label"><span class="dot-icon"></span> عدد الإعلانات </span><span class="value">: 12</span></div><div class="item"><span class="label"><span class="dot-icon"></span> النقاط المتاحة </span><span class="value">: 250</span></div><div class="item"><span class="label"><span class="dot-icon"></span> الميزات والعلامة التجارية: </span></div><ul><li><span class="dot-icon"></span> شارة مميزة</li><li><span class="dot-icon"></span> دعم العلامة التجارية</li><li><span class="dot-icon"></span> تحليلات متقدمة</li></ul><div class="item"><span class="label"><span class="dot-icon"></span> الدعم </span><span class="value">: 24/7 عبر واتساب</span></div></div>',
    packageValidDays: 30,
    isCurrentPackage: false,
    packageEndDate: null, 
  },
  {
    id: 2,
    name: "Pro",
    currency: "EGP",
    price: 299,
    labelTag: null,
    renewableLabel: "It_will_be_renewed_automatically_after",
    htmlDescription:
      '<div class="package-details"><div class="item"><span class="label"><span class="dot-icon"></span> الصلاحية </span><span class="value">: 30 يوم</span></div><div class="item"><span class="label"><span class="dot-icon"></span> عدد الإعلانات </span><span class="value">: 12</span></div><div class="item"><span class="label"><span class="dot-icon"></span> النقاط المتاحة </span><span class="value">: 250</span></div><div class="item"><span class="label"><span class="dot-icon"></span> الميزات والعلامة التجارية: </span></div><ul><li><span class="dot-icon"></span> شارة مميزة</li><li><span class="dot-icon"></span> دعم العلامة التجارية</li><li><span class="dot-icon"></span> تحليلات متقدمة</li></ul><div class="item"><span class="label"><span class="dot-icon"></span> الدعم </span><span class="value">: 24/7 عبر واتساب</span></div></div>',
    packageValidDays: 30,
    isCurrentPackage: true,
    packageEndDate: "28 يناير 2025", 
  },
  {
    id: 3,
    name: "Enterprise",
    currency: "EGP",
    price: 999,
    labelTag: null,
    renewableLabel: "It_will_be_renewed_automatically_after",
    htmlDescription:
      '<div class="package-details"><div class="item"><span class="label"><span class="dot-icon"></span> الصلاحية </span><span class="value">: 30 يوم</span></div><div class="item"><span class="label"><span class="dot-icon"></span> عدد الإعلانات </span><span class="value">: 12</span></div><div class="item"><span class="label"><span class="dot-icon"></span> النقاط المتاحة </span><span class="value">: 250</span></div><div class="item"><span class="label"><span class="dot-icon"></span> الميزات والعلامة التجارية: </span></div><ul><li><span class="dot-icon"></span> شارة مميزة</li><li><span class="dot-icon"></span> دعم العلامة التجارية</li><li><span class="dot-icon"></span> تحليلات متقدمة</li></ul><div class="item"><span class="label"><span class="dot-icon"></span> الدعم </span><span class="value">: 24/7 عبر واتساب</span></div></div>',
    packageValidDays: 90,
    isCurrentPackage: false,
    packageEndDate: "15 مارس 2025", 
  },
];

const packagesServiceMock = {
  selectPlan: (id: number) => console.log("Mock selectPlan:", id),
};

const viewSwitchServiceMock = {
  go: (route: string) => console.log("Mock navigate to:", route),
};

const meta: Meta<PackagePlanComponent> = {
  title: "BusinessPackages/BusinessPackagePlan",
  component: PackagePlanComponent, 
  tags: ["autodocs"],
  decorators: [
    moduleMetadata({
      providers: [
        { provide: PackagesService, useValue: packagesServiceMock },
        { provide: ViewSwitchService, useValue: viewSwitchServiceMock },
      ],
    }),
  ],
  render: (args) => ({ props: args }),
};

export default meta;
type Story = StoryObj<PackagePlanComponent>;


export const Subscribe: Story = {
  args: { plan: mockPlans[0] },
};

export const CurrentPackage: Story = {
  args: { plan: mockPlans[1] },
};

export const ChangePackage: Story = {
  args: { plan: mockPlans[2] },
};
