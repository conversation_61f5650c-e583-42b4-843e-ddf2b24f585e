@import "variables";

.filter-panel {
    background: #fff; // you could also define a $white var if used often
    width: 100%;
    max-width: 400px;

    .section-title {
        font-size: 16px;
        font-weight: 800;
        margin-top: 20px;
        margin-bottom: 10px;
        color: rgba(39, 39, 40, 0.7);
    }

    .filter-label {
        font-weight: 700;
        margin-bottom: 8px;
        font-size: 12px;
        margin-top: 16px;
        color: rgb(124, 124, 124, 0.7);
    }

    .p-button {
        font-size: 13px;
        padding: 6px 10px;
        font-family: $f-r;
    }

    .p-calendar {
        margin-bottom: 20px;
    }
}

.quick-buttons {
    display: flex;
    gap: 0.5rem;
    margin-bottom: 1rem;
    justify-content: flex-start;
    flex-wrap: wrap;
}

button {
    padding: 0.2rem 0.4rem;
    border-radius: 6px;
    border: 1px solid rgba(114, 34, 130, 0.2);
    background-color: #fff;
    color: rgb(114, 34, 130, 0.7);
    font-weight: 700;
    font-size: 10px;

    &.active {
        background-color: rgba(114, 34, 130, 0.2);
    }
}