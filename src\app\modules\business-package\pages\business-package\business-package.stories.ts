import { Meta, moduleMetadata, StoryObj } from "@storybook/angular";
import { BusinessPackageComponent } from "./business-package.component";
import { PackagesService } from "../../services/packages.service";
import { ViewSwitchService } from "../../services/view-switch.service";
import { DynamicDialogRef } from "primeng/dynamicdialog";
import { PackagePlan } from "../../models/packages.model";

const mockPlans: PackagePlan[] = [
  {
    id: 1,
    name: "Basic",
    currency: "EGP",
    price: 0,
    labelTag: null,
    renewableLabel: "It_will_be_renewed_automatically_after",
    htmlDescription:
      '<div class="package-details"><div class="item"><span class="label"><span class="dot-icon"></span> الصلاحية </span><span class="value">: 30 يوم</span></div><div class="item"><span class="label"><span class="dot-icon"></span> عدد الإعلانات </span><span class="value">: 12</span></div><div class="item"><span class="label"><span class="dot-icon"></span> النقاط المتاحة </span><span class="value">: 250</span></div><div class="item"><span class="label"><span class="dot-icon"></span> الميزات والعلامة التجارية: </span></div><ul><li><span class="dot-icon"></span> شارة مميزة</li><li><span class="dot-icon"></span> دعم العلامة التجارية</li><li><span class="dot-icon"></span> تحليلات متقدمة</li></ul><div class="item"><span class="label"><span class="dot-icon"></span> الدعم </span><span class="value">: 24/7 عبر واتساب</span></div></div>',
    packageValidDays: 30,
    isCurrentPackage: false,
    packageEndDate: "15 مارس 2025", 

  },
  {
    id: 2,
    name: "Pro",
    currency: "EGP",
    price: 299,
    labelTag: "الأكثر شيوعًا",
    renewableLabel: "It_will_be_renewed_automatically_after",
    htmlDescription:
      '<div class="package-details"><div class="item"><span class="label"><span class="dot-icon"></span> الصلاحية </span><span class="value">: 30 يوم</span></div><div class="item"><span class="label"><span class="dot-icon"></span> عدد الإعلانات </span><span class="value">: 12</span></div><div class="item"><span class="label"><span class="dot-icon"></span> النقاط المتاحة </span><span class="value">: 250</span></div><div class="item"><span class="label"><span class="dot-icon"></span> الميزات والعلامة التجارية: </span></div><ul><li><span class="dot-icon"></span> شارة مميزة</li><li><span class="dot-icon"></span> دعم العلامة التجارية</li><li><span class="dot-icon"></span> تحليلات متقدمة</li></ul><div class="item"><span class="label"><span class="dot-icon"></span> الدعم </span><span class="value">: 24/7 عبر واتساب</span></div></div>',
    packageValidDays: 30,
    isCurrentPackage: true,
    packageEndDate: "28 يناير 2025", 
  },
  {
    id: 3,
    name: "Enterprise",
    currency: "EGP",
    price: 999,
    labelTag: null,
    renewableLabel: "It_will_be_renewed_automatically_after",
    htmlDescription:
      '<div class="package-details"><div class="item"><span class="label"><span class="dot-icon"></span> الصلاحية </span><span class="value">: 90 يوم</span></div><div class="item"><span class="label"><span class="dot-icon"></span> عدد الإعلانات </span><span class="value">: حسب الطلب</span></div><div class="item"><span class="label"><span class="dot-icon"></span> النقاط المتاحة </span><span class="value">: حسب الطلب</span></div><div class="item"><span class="label"><span class="dot-icon"></span> الميزات والعلامة التجارية: </span></div><ul><li><span class="dot-icon"></span> جميع ميزات Power Seller</li><li><span class="dot-icon"></span> تكامل API</li><li><span class="dot-icon"></span> تحليلات تفصيلية</li></ul><div class="item"><span class="label"><span class="dot-icon"></span> الدعم </span><span class="value">: SLA خاص</span></div></div>',
    packageValidDays: 90,
    isCurrentPackage: false,
    packageEndDate: "15 مارس 2025", 
  },
];

const packagesServiceMock = {
  plans: () => mockPlans,                 
  selectPlan: (id: number) => console.log("Mock selectPlan:", id),
};

const viewSwitchServiceMock = {
  go: (route: string) => console.log("Mock go:", route),
  reset: () => console.log("Mock reset"),
};

const dialogRefMock = {
  close: (payload?: any) => console.log("Mock close:", payload),
};


const meta: Meta<BusinessPackageComponent> = {
  title: "BusinessPackages/BusinessPackage",
  component: BusinessPackageComponent, 
  tags: ["autodocs"],
  decorators: [
    moduleMetadata({
      providers: [
        { provide: PackagesService, useValue: packagesServiceMock },
        { provide: ViewSwitchService, useValue: viewSwitchServiceMock },
        { provide: DynamicDialogRef, useValue: dialogRefMock },
      ],
    }),
  ],
  parameters: { layout: "centered" },
  render: (args) => ({ props: args }),
};

export default meta;
type Story = StoryObj<BusinessPackageComponent>;

export const Default: Story = {
  args: {},
};
