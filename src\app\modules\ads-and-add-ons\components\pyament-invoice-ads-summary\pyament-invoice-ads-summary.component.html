<app-ad-addons-widget [addon]="ad()" [reviewMode]="true" />


<div class="meta">
    <div class="row">
        <span class="label">على الاعلان: <span class="value">شقة للبيع متشطبة استلام فوري</span></span>

    </div>
</div>

<ng-container *ngIf="this.adsSvc.stage()  === AdsStage.Details;else invoice">

    <div>
        <div class="image_placeholder" *ngIf="ad().id !== 4 ; else notificationDetials">
            <img src="../../../../../../assets/images/Ad Bump.png" alt="">
        </div>

        <div class="section">
            <app-points-status-widget [current]="1000" [pointsRequired]="ad().costValue" [usedPoints]="ad().costValue"
                [showDetials]="true" />
        </div>

        <div class="section">
            <app-shared-btn label="اضافة و تأكيد سحب 2,000 نقطة" (btnClick)="onAddInvoice()"></app-shared-btn>
        </div>
    </div>

    <ng-template #notificationDetials>
        <div class="title">شكل الرسالة</div>
        <img src="../../../../../../assets/images/Push Notification.png" alt="" class="image-margin">

        <form [formGroup]="form" class="schedule-form">
            <div class="title">تاريخ الارسال</div>

            <label class="field-label">اليوم</label>
            <p-calendar formControlName="day" [showIcon]="true" [iconDisplay]="'input'" styleClass="purple-text">
                <ng-template pTemplate="inputicon" let-clickCallBack="clickCallBack">
                    <button type="button" class="p-datepicker-trigger custom-icon" (click)="clickCallBack($event)">
                        <app-svg-icons width="16px" height="16px" name="calender-icon"></app-svg-icons>
                    </button>
                </ng-template>
            </p-calendar>

            <label class="field-label">الساعة</label>
            <p-calendar formControlName="time" [showIcon]="true" [iconDisplay]="'input'" [timeOnly]="true"
                styleClass="purple-text">
                <ng-template pTemplate="inputicon" let-clickCallBack="clickCallBack">
                    <button type="button" class="p-datepicker-trigger custom-icon" (click)="clickCallBack($event)">
                        <app-svg-icons width="16px" height="16px" name="date-icon"></app-svg-icons>
                    </button>
                </ng-template>
            </p-calendar>

        </form>
    </ng-template>

</ng-container>

<ng-template #invoice>


    <hr />

    <div class="meta">
        <div class="row">
            <span class="label">إضافة التنبيهات: <span class="value">5,000 نقطة</span></span>

        </div>
        <div class="row">
            <span class="label">سحب النقط المتاحة: <span class="value">1,000- نقطة</span></span>

        </div>
        <div class="row">
            <span class="label">شراء نقط: <span class="value">4,000 نقطة</span></span>

        </div>

        <hr />

        <div class="row">
            <span class="label">الإجمالي الفرعي: <span class="value">200&nbsp;جنيه</span></span>

        </div>

        <div class="row">
            <span class="label">القيمة المضافة: <span class="value">14%</span></span>

        </div>

        <div class="row">
            <span class="label">المجموع: <span class="value">200&nbsp;جنيه</span></span>

        </div>


    </div>

    <div class="section">
        <app-shared-btn label=" دفع  200 جنية" (btnClick)="onAdd()"></app-shared-btn>
    </div>

</ng-template>