import { Component, Input } from '@angular/core';
import { NtranslatePipe } from '../../pipes/ntranslate.pipe';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-stars-widget',
  standalone: true,
  imports: [CommonModule, NtranslatePipe],
  templateUrl: './stars-widget.component.html',
  styleUrl: './stars-widget.component.scss'
})
export class StarsWidgetComponent {
  @Input() ratingValue = 3.5;
  @Input() numberOfRating = 24;

  get full(): number {
    return Math.floor(this.roundToHalf(this.ratingValue));
  }

  get hasHalf(): boolean {
    return this.roundToHalf(this.ratingValue) % 1 === 0.5;
  }

  private roundToHalf(v: number): number {
    return Math.round(v * 2) / 2;
  }
}
