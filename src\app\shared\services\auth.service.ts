import { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Router } from '@angular/router';
import { BehaviorSubject, Observable } from 'rxjs';
import {
  ChangePasswordDTO,
  ForgotPasswordDto,
  ResetPasswordDto,
} from 'src/app/authentication/models/dto/password.dto.model';
import {
  RegisterUserWithOTP,
  User,
  UserForAuthenticationDto,
  UserForRegistrationDto,
  VerifyPhoneNumber,
} from 'src/app/authentication/models/dto/user.dto.model';


import { CustomEncoder } from '@services/custom-encoer';
import { AuthResponse } from 'src/app/authentication/models/responses/auth.response.model';
import { environment } from 'src/environments/environment';

import { BaseResponse } from "@shared/models/base.response.model";
import { OAuthService } from 'angular-oauth2-oidc';
import { ExternalAuthDto } from 'src/app/authentication/models/dto/externalauth.dto.model';
import { TwoFactorDto } from 'src/app/authentication/models/dto/teofactor.dto.mode';
import { RegistrationResponseDto } from 'src/app/authentication/models/responses/registration.response.mode';
import { BrowserService } from 'src/app/modules/core/service/browser.service';
@Injectable({
  providedIn: 'root',
})
export class AuthService {
  private userSubject: BehaviorSubject<User>;
  public user: Observable<User>;

  constructor(
    private router: Router,
    private http: HttpClient,
    private browser: BrowserService,
    private oauthService: OAuthService
  ) {
    this.userSubject = new BehaviorSubject<User>(new User());
    this.user = this.userSubject.asObservable();

  }
  public get userValue(): User {
    return this.userSubject.value;
  }
  public registerUser = (route: string, body: UserForRegistrationDto) => {
    return this.http.post<RegistrationResponseDto>(
      this.createCompleteRoute(route, environment.authAPI),
      body
    );
  };
  public registerUserWithOTP = (route: string, body: RegisterUserWithOTP) => {
    return this.http.post<RegistrationResponseDto>(
      this.createCompleteRoute(route, environment.authAPI),
      body
    );
  };
  uploadImgs(formData: FormData) {
    return this.http.post<string>(
      `${environment.authAPI}/api/Profile/saveimg`,
      formData,
      {
        observe: 'response',
        reportProgress: true,
        responseType: 'json',
      }
    );
  }
  login(body: UserForAuthenticationDto) {
    return this.http.post<AuthResponse>(
      `${environment.authAPI}/api/account/login`,
      body
    );
  }
  verfiyPhoneNumber(body: VerifyPhoneNumber) {
    return this.http.post<AuthResponse>(
      `${environment.authAPI}/api/verify_phone_number`,
      body
    );
  }
  phoneNumberExists(phone: string) {
    return this.http.get<boolean>(
      `${environment.authAPI}/api/account/phoneexists?phonenumber=` + phone
    );
  }
  generateotpforregistration(phone: string) {
    let defualtLang = this.browser.getStorageItem(environment.storage.lang) || this.browser.getWindowVariable('defaultLang');
    let headers = new HttpHeaders();
    headers.set("Culture", defualtLang!);
    // return of(true).pipe(map((x) => true));
    return this.http.get(
      `${environment.authAPI}/api/account/generateotpforregistration?phoneNumber=` +
      phone, { headers: headers }
    );
  }
  setUserLogged() { }
  redirectLogin(reserve: boolean = true) {
    let defualtLang = this.browser.getStorageItem(environment.storage.lang) || this.browser.getWindowVariable('defaultLang');

    if (reserve) {
      if (this.router.url.includes('/authentication/register'))
        this.browser.setStorageItem('redirectUri', "/home");
      else
        this.browser.setStorageItem('redirectUri', this.router.url);
    }



    this.oauthService.initImplicitFlow();

  }

  logout() {
    let defualtLang = this.browser.getStorageItem(environment.storage.lang) || this.browser.getWindowVariable('defaultLang');

    // remove user from local storage to log user out
    // this.oidcSecurityService.revokeAccessToken(this.browser.getStorageItem('access_token'));
    // this.oidcSecurityService.logoff();
    //window.location.href = environment.clintURL + '/' + defualtLang + '/home';

    this.browser.removeStorageItem('access_token');
    this.browser.removeStorageItem('id');
    this.browser.removeStorageItem('user');
    // this.browser.removeStorageItem('refreshToken');
    this.browser.removeStorageItem('userFavourites');
    
    // Clear access token cookie
    if (this.browser.isBrowser()) {
      document.cookie = 'access_token=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;';
    }
    
    this.userSubject.next(new User());
    // this.router.navigate(['/home']);

    this.oauthService.logOut();
  }
  public forgotPassword = (route: string, body: ForgotPasswordDto) => {
    return this.http.post(
      this.createCompleteRoute(route, environment.authAPI),
      body
    );
  };
  public twoStepLogin = (route: string, body: TwoFactorDto) => {
    return this.http.post<AuthResponse>(
      this.createCompleteRoute(route, environment.authAPI),
      body
    );
  };

  public resetPassword = (route: string, body: ResetPasswordDto) => {
    return this.http.post(
      this.createCompleteRoute(route, environment.authAPI),
      body
    );
  };

  sendUserChanged(user: User) {
    this.browser.setStorageItem('user', user.token);
    this.userSubject.next(user);
  }
  // public isUserAuthenticated = (): boolean => {
  //   const token = this.browser.getStorageItem('access_token') || '';
  //   if (token == 'null') return false;
  //   return !this._jwtHelper.isTokenExpired(token);
  // };

  public isUserAuthenticated = async (): Promise<boolean> => {
    //return (await this.oidcSecurityService.isAuthenticated().toPromise()) ?? false;

    return new Promise((r, j) => true);
  };

  // public isUserAuthenticated = (): boolean => {
  //   if (this.oidcSecurityService.isAuthenticated()) return true;

  //   const token = this.browser.getStorageItem('access_token') || '';
  //   if (token == '') return false;
  //   else {
  //     if (this.oidcSecurityService.isAuthenticated()) return true;
  //   }
  //   return false;
  // };

  public confirmEmail = (
    token: string,
    email: string,
    userName: string
  ) => {


    let params = new HttpParams({ encoder: new CustomEncoder() });
    params = params.append('token', token);
    params = params.append('newEmail', email);
    params = params.append('userName', userName);

    return this.http.get(environment.authAPI + '/api/account/emailconfirmation', {
      params: params,
    });
  };
  public confirmPhone = (token: string, user: User) => {
    let params = new HttpParams({ encoder: new CustomEncoder() });
    params = params.append('token', token);

    return this.http.post<AuthResponse>(
      this.createCompleteRoute(
        'api/account/phonenumberconfirmation',
        environment.authAPI
      ) +
      '?token=' +
      token,
      user
    );
  };

  public requestPhoneNumberConfirmation = (user: User) => {
    return this.http.post(
      this.createCompleteRoute(
        'api/account/userphonenumber',
        environment.authAPI
      ),
      user
    );
  };

  public externalLogin = (route: string, body: ExternalAuthDto) => {
    return this.http.post<AuthResponse>(
      this.createCompleteRoute(route, environment.authAPI),
      body
    );
  };
  changePassword(model: ChangePasswordDTO) {
    return this.http.post<BaseResponse<string>>(
      this.createCompleteRoute(
        'api/account/changepassword',
        environment.authAPI
      ),
      model
    );
  }
  getUserObject(token: string) {
    // const userTokenObject = this._jwtHelper.decodeToken(token);

    // let user = new User();

    // user.token = token;
    // user.userName =
    //   userTokenObject[
    //     'http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name'
    //   ];
    // user.refreshToken = this.browser.getStorageItem('refreshToken');
    // user.role =
    //   userTokenObject[
    //     'http://schemas.microsoft.com/ws/2008/06/identity/claims/role'
    //   ];
    // user.userImage = this.browser.getStorageItem('userImage');
    // user.id = userTokenObject['id'];
    return this.userSubject.value;
    // return user;
  }
  public checkEmailExist(email: string) {
    return this.http.get(
      `${environment.authAPI}/api/account/emailexists?email=` + email
    );
  }
  public googleConnect() {
    return this.http.get(
      `${environment.authAPI}/api/profile/challenge?scheme=Google&returnUrl=` + environment.clintURL
    );
  }
  public signInWithGoogle = () => {
    return undefined;
  };
  public signInWithFB = () => {
    return undefined;
  };
  public signOutExternal = () => {
    //this._externalAuthService.signOut();
  };
  private createCompleteRoute = (route: string, envAddress: string) => {
    return `${envAddress}/${route}`;
  };
  public isUserRole = (): boolean => {
    const role = this.userSubject.value.role;
    if (role) return role.toLowerCase() === 'user';
    return false;
  };
  public subscribeUser(userData: any) {
    const user = userData;
    if (user) {
      this.userSubject = new BehaviorSubject<User>(user);
      this.user = this.userSubject.asObservable();
    } else {
      this.userSubject = new BehaviorSubject<User>(new User());
      this.user = this.userSubject.asObservable();
    }
  }


  private offsetSeconds = 30;
  private refreshSubscription: any;
  public redirectUrl!: string;
  private signinStatus = new BehaviorSubject<boolean>(false);

  private refreshTokenTimeout;

  stopRefreshTokenTimer() {
    clearTimeout(this.refreshTokenTimeout);
  }

  userEmail(user: User, clientURL: string) {
    return this.http.post(
      this.createCompleteRoute(
        'api/account/useremail?clienturi=' + clientURL,
        environment.authAPI
      ),
      user
    );
  }

  refreshToken() {
    this.oauthService.silentRefresh().then((res) => {

    });

  }
}
