import { AfterViewInit, Component, ElementRef, Input, OnChanges, OnInit, SimpleChanges } from '@angular/core';
import { ProfileModel, UserSettingModel } from '../../models/profile.model';
import { environment } from '@src/environments/environment';
import { SellerMemebershipIconComponent } from '@src/app/modules/seller/components/seller-memebership-icon/seller-memebership-icon.component';
import { LazyloadDirective } from '../../directives/lazyload.directive';
import { CommonModule } from '@angular/common';

interface ProfileModelDTO {
  userID?: string;
  setting?: UserSettingModel;
  businessName?: string;
}

@Component({
  selector: 'app-profile-image',
  standalone: true,
  imports: [CommonModule, LazyloadDirective, SellerMemebershipIconComponent],
  templateUrl: './profile-image.component.html',
  styleUrl: './profile-image.component.scss'
})
export class ProfileImageComponent implements OnInit, AfterViewInit, OnChanges {

  @Input() user: ProfileModelDTO;

  sellerImage: string;

  constructor(private elementRef: ElementRef) { }
  ngOnChanges(changes: SimpleChanges): void {
    this.sellerImage = environment.userMediaPath + this.user.userID + '.jpg?v=' + new Date().getTime();
  }

  ngOnInit(): void {
    this.sellerImage = environment.userMediaPath + this.user.userID + '.jpg?v=' + new Date().getTime();
  }

  ngAfterViewInit(): void {
    if (this.user.setting) {
      this.elementRef.nativeElement.style.setProperty(
        '--main-membership-color',
        this.user.setting?.membership.color
      );
    }

  }

}