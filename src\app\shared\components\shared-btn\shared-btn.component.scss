@import "variables";

.shared-btn {
  width: 100%;
  background: linear-gradient(180deg, rgba(255, 255, 255, 0.12) 0%, rgba(255, 255, 255, 0) 100%), #722282;
  overflow: hidden;
  border-radius: 6px;
  outline: 1px rgba(255, 255, 255, 0.12) solid;
  outline-offset: -1px;
  justify-content: center;
  align-items: center;
  display: inline-flex;
  cursor: pointer;

  &--small {
    height: 26px;

    .label {
      font-size: 10px;
    }
  }

  &--medium {
    height: 38px;

    .label {
      font-size: 14px;
    }
  }

  &--large {
    height: 48px;

    .label {
      font-size: 14px;
    }
  }

  /* Themes */
  &--normal {
    background: linear-gradient(180deg,
        rgba(255, 255, 255, 0.12) 0%,
        rgba(255, 255, 255, 0) 100%),
      #722282;
    box-shadow: 0px 0px 0px 1px #6b367f;
    outline: 1px rgba(114, 34, 130, 0.2) solid;

    .label {
      color: white;
    }
  }

  &--bgWhite {
    background: white;
    box-shadow: 0px 0px 0px 1px #ddd;

    .label {
      color: #722282 !important;
    }
  }

  &--error {
    background: rgba(177, 54, 47, 1);
    box-shadow: 0px 0px 0px 1px #B1362F;

    .label {
      color: white;
    }
  }

  &disabled,
  &:disabled,
  &[aria-disabled='true'] {
    .label {
      color: #72228233;
    }

    opacity: 0.6;
    cursor: not-allowed;
    pointer-events: none;
    background: #EFEFEF;
    box-shadow: none;
  }
}

.label {
  text-align: center;
  color: white;
  font-family: Cairo;
  font-weight: 700;
  word-wrap: break-word;
  display: inline-flex;
  align-items: center;
  gap: 6px;
}