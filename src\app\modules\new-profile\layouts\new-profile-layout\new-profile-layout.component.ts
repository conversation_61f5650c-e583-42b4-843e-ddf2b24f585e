import { Component } from "@angular/core";
import { RouterModule } from "@angular/router";
import { ProfileService } from "@src/app/shared/services/profile.service";

@Component({
  selector: "app-profile-layout",
  standalone: true,
  imports: [
    RouterModule
  ],
  templateUrl: "./new-profile-layout.component.html",
  styleUrl: "./new-profile-layout.component.scss",
})
export class NewProfileLayoutComponent {

  constructor(
    private profileService: ProfileService,
  ) { }

  ngOnInit(): void {

    this.profileService.getUserProfile()
      .subscribe(res => this.profileService.setProfile(res));

  }
}