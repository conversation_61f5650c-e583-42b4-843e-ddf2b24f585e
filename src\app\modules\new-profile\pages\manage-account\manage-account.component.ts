import { Store } from '@ngrx/store';
import { BrowserService } from './../../../core/service/browser.service';
import { CommonModule } from "@angular/common";
import { Component } from "@angular/core";
import { ProfileCoverComponent } from "@src/app/modules/profile/components/profile-cover/profile-cover.component";
import { ProfileModel } from "@src/app/shared/models/profile.model";
import { ProfileService } from "@src/app/shared/services/profile.service";
import { map, Observable, tap } from "rxjs";
import { NewProfileHeaderComponent } from "../../components/new-profile-header/new-profile-header.component";
import { SvgIconsComponent } from "@src/app/shared/components/svg-icons/svg-icons.component";
import { ListingCardRowComponent } from "@src/app/shared/components/listing-card-row/listing-card-row.component";
import { SellerFilter } from '@src/app/shared/models/preview.model';
import { PreviewService } from '@src/app/shared/services/preview.service';
import { BasePagenatedResponse } from '@src/app/shared/models/base.response.model';
import { ListingView } from '@src/app/shared/models/listing.model';
import { PointsStatusWidgetComponent } from '@src/app/shared/components/points-status-widget/points-status-widget.component';
import { Router } from '@angular/router';
import { SharedBtnComponent } from '@src/app/shared/components/shared-btn/shared-btn.component';
@Component({
  selector: "app-manage-account",
  standalone: true,
  imports: [ProfileCoverComponent,
    CommonModule,
    NewProfileHeaderComponent,
    SvgIconsComponent,
    ListingCardRowComponent,
    PointsStatusWidgetComponent,
    SharedBtnComponent
  ],
  templateUrl: "./manage-account.component.html",
  styleUrl: "./manage-account.component.scss",
})
export class ManageAccountComponent {
  profile: ProfileModel;
  listing$: Observable<BasePagenatedResponse<ListingView> | null>;

  isBussinessAccount = false;

  sellerID: any
  filter: SellerFilter = {};

  ads = [
    {
      id: 1,
      name: 'شقة للبيع متشطبي',
      webpImageURL: '../../../../../../assets/images/image 4.jpg',
      callCount: 200,
      views: 32,
      appearanceCount: 103,
      tags: ['Top Featured'],
    },
    {
      id: 2,
      name: 'شقة للبيع متشطبة أس...',
      webpImageURL: '../../../../../../assets/images/image 4.jpg',
      callCount: 200,
      views: 32,
      appearanceCount: 103,
      tags: ['Top Featured'],
    },
  ];


  constructor(private browserService: BrowserService,
    private profileService: ProfileService,
    private _previewService: PreviewService,
    private router: Router
  ) { }

  ngOnInit(): void {

    if (this.browserService.isBrowser()) {

      this.profileService.profile.subscribe((res) => {
        this.profile = res
        this.sellerID = res.userID;
        this.getListings();
      })

    }

  }

  getListings() {

    this.filter = {
      ...this.filter,
      sellerId: this.sellerID,
      pageNumber: 1,
      pageSize: 20,
      sortBy: 'firstApprovalDate',
      isSortAscending: true,
      status: 2
    };

    this.listing$ = this._previewService.getSellerListing(this.filter).pipe(map(res => res.body?.data ?? null), tap(
      res => {
        // this.pagination.totalItems = res?.totalCount;
        // this.pagination.totalPages = res?.totalPages;
      }

    ));

  }

  goToPoints() {
    this.router.navigate(['authentication/new-profile/points'])
  }
}