@import "variables";

.promo-card {
    background: rgba(238, 238, 238, 1); // light grey background
    border-radius: 6px;
    padding: 16px;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    inline-size: 100%;
    box-sizing: border-box;
}

.header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 1rem;

    .title-wrap {
        display: flex;
        align-items: center;
        gap: 6px;

        .title {
            font-weight: 700;
            font-size: 16px;
            color: $primary; // purple from variables
            font-family: $f-b;
        }
    }
}

.desc {
    font-size: 14px;
    line-height: 1.5;
    color: $text-color;
    margin: 0;
    font-family: $f-r;
}

.meta {
    display: flex;
    flex-direction: column;
    gap: 4px;

    .row {
        display: flex;
        gap: 4px;
        font-size: 14px;
        color: $text-color;

        .label {
            font-weight: 700;
            padding: 0px;
        }

        .value {
            font-weight: 400;
        }
    }
}

.read-more {
    background: none;
    font-size: 12px;
    font-weight: 700;
    color: $primary;
    display: inline-flex;
    gap: 4px;
    cursor: pointer;
    margin-bottom: 0.4rem;

    .arrow {
        font-weight: 700;
    }

    &:hover {
        text-decoration: underline;
    }
}

.cta {
    background: linear-gradient(180deg, lighten($primary, 10%), $primary);
    color: #fff;
    font-size: 16px;
    font-weight: 700;
    padding: 10px;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    text-align: center;
    margin-top: auto;
    font-family: $f-b;

    &:disabled {
        background: $text-muted-3;
        cursor: not-allowed;
    }
}

.auto-renew {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 2px 12px;
    border-radius: 3px;
    background: rgb(217, 217, 217);
    font-size: 12px;

    strong {
        font-weight: 800;
    }
}

::ng-deep app-svg-icons[name="back-icon"] svg {
    transform: rotate(var(--arrow-rotation));
}