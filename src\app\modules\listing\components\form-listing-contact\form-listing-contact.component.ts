import { CommonModule } from '@angular/common';
import { Component, inject, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { Control<PERSON>ontainer, FormBuilder, FormControl, FormGroupDirective, ReactiveFormsModule, Validators } from '@angular/forms';
import { NtranslatePipe } from '@src/app/shared/pipes/ntranslate.pipe';
import { FormUtils } from '@src/utils/form-utils';

@Component({
  selector: 'app-form-listing-contact',
  standalone: true,
  imports: [CommonModule, NtranslatePipe, ReactiveFormsModule],
  templateUrl: './form-listing-contact.component.html',
  styleUrl: './form-listing-contact.component.scss',
  viewProviders: [
    {
      provide: ControlContainer, useExisting: FormGroupDirective
    }
  ]
})
export class FormListingContactComponent implements OnInit, OnDestroy {

  parentForm = inject(FormGroupDirective);
  fb = inject(FormBuilder);

  private readonly phoneValidators = [
    Validators.required,
    Validators.pattern(/^01[0-5]\d{1,8}/),
    Validators.minLength(11),
  ];

  contactForm = this.fb.group({
    listingContactName: ['', [
      FormUtils.nameValidator,
      FormUtils.lengthValidator
    ]],
    listingContactPhone: this.createConditionalPhoneControl()
  });

  ngOnInit(): void {
    this.parentForm.control.addControl('contact', this.contactForm);
  }

  ngOnDestroy(): void {
    this.parentForm.control.removeControl('contact');
  }

  private createConditionalPhoneControl(): FormControl {
    const control = new FormControl('');

    control.valueChanges.subscribe(value => {
      const hasValue = value && value.trim();
      control.setValidators(hasValue ? this.phoneValidators : null);
      control.updateValueAndValidity({ emitEvent: false });
    });

    return control;
  }



}
