import { Component } from '@angular/core';
import { AdsAndAddOnsService } from '../../services/ads-and-add-ons.service';

@Component({
  selector: 'app-success-popup-ad-addons',
  standalone: true,
  imports: [],
  templateUrl: './success-popup-ad-addons.component.html',
  styleUrl: './success-popup-ad-addons.component.scss'
})
export class SuccessPopupAdAddonsComponent {

  constructor(private ads: AdsAndAddOnsService) { }
  reset() {
    this.ads.reset();
  }
}
