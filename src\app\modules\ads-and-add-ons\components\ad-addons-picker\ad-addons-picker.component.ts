import { AdsStage } from './../../services/ads-and-add-ons.service';
import { AdsAndAddOnsService } from '@src/app/modules/ads-and-add-ons/services/ads-and-add-ons.service';
import { Component, EventEmitter, Input, Output, Signal } from '@angular/core';
import { CommonModule } from '@angular/common';
import { AdAddonsWidgetComponent } from '@src/app/shared/components/ad-addons-widget/ad-addons-widget.component';
import { AdsAndAddOns } from '../../models/ads-and-add-ons.model';
import { SvgIconsComponent } from '@src/app/shared/components/svg-icons/svg-icons.component';



@Component({
  selector: 'app-ad-addons-picker',
  standalone: true,
  imports: [CommonModule, AdAddonsWidgetComponent, SvgIconsComponent],
  templateUrl: './ad-addons-picker.component.html',
  styleUrls: ['./ad-addons-picker.component.scss'],
})
export class AdAddonsPickerComponent {

  AdsStage = AdsStage


  @Input() disabled = false;

  @Output() close = new EventEmitter<void>();
  @Output() add = new EventEmitter<string>();
  @Output() extend = new EventEmitter<string>();
  @Output() readMore = new EventEmitter<string>();

  constructor(private adsAndAddOnsService: AdsAndAddOnsService) { }

  ads: Signal<AdsAndAddOns[]> = this.adsAndAddOnsService.ads;

  onClose() { this.close.emit(); }
  onAdd(id: string) { if (!this.disabled) this.add.emit(id); }
  onExtend(id: string) { if (!this.disabled) this.extend.emit(id); }
  onReadMore(id: string) { this.readMore.emit(id); }

  openDetials(aid: number) {
    this.adsAndAddOnsService.selectById(aid);
    this.adsAndAddOnsService.goTo(AdsStage.Details)
  }

  openInvice(isSubscribed: boolean, aid: number) {
    this.adsAndAddOnsService.selectById(aid);
    if (isSubscribed)
      this.adsAndAddOnsService.goTo(AdsStage.ExtendDuration)
    else if (aid === 4)
      this.adsAndAddOnsService.goTo(AdsStage.Details)
    else
      this.adsAndAddOnsService.goTo(AdsStage.Invoice)
  }
}
