import { Meta, moduleMetadata, StoryObj } from '@storybook/angular';
import { StarsWidgetComponent } from './stars-widget.component';

const meta: Meta<StarsWidgetComponent> = {
    title: 'Shared/Stars Widget',
    component: StarsWidgetComponent,
    decorators: [
        moduleMetadata({
            providers: [],
        }),
    ],
    tags: ['autodocs'],
};

export default meta;
type Story = StoryObj<StarsWidgetComponent>;

export const Default: Story = {
    args: {
        ratingValue: 3.5,
        numberOfRating: 50
    },
};

export const StoryOne: Story = {
    args: {
        ratingValue: 3,
        numberOfRating: 60
    }
};
