.container{
  padding: 24px;
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

.points-card {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.points-row {
  display: flex;
  align-items: center;
}

.price {
  font-size: 16px;
  font-weight: 800;
  line-height: 15px;
  direction: ltr;
}

.points-actions {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 8px;
}

.buy-btn{
  background: #ECFDF3;
  border-radius: 6px;
  padding: 8px 10px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.buy-btn span {
  color: #027A48;
  font-size: 12px;
  font-weight: 600;
  line-height: 15.5px;
}

.points-text {
  font-size: 16px;
  font-weight: 800;
  line-height: 15px;
}

.points-info {
  display: flex;
  gap: 16px;
  width: 100%;
}

.info-text {
  color: #7C7C7C;
  font-size: 12px;
  font-weight: 700;
  line-height: 15.5px;
}

.info-date {
  display: flex;
  justify-content: flex-end;
  align-items: flex-start;
  gap: 4px;
}

.info-date span {
  color: #7C7C7C;
  font-size: 12px;
  font-weight: 600;
  line-height: 15.5px;
}

.info-date .label {
  font-weight: 700;
}
.divider {
  width: 100%;
  margin:15px 0px;
  outline: 1px solid rgba(114, 34, 130, 0.10);
}

.points-empty {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center; 
  align-items: center;   
  gap: 6px;
  text-align: center;
  height: 100%; 
}

.points-empty__text {
  color: #A3A4A5;
  font-weight: 700;
  font-size: 10px;
  line-height: 28px;
  white-space: nowrap;
}

:host ::ng-deep .header-summary {
  margin-bottom: 0rem !important;
}