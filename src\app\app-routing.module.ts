import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { BlankComponent } from './components/blank/blank.component';
import { ShortenComponent } from './components/shorten/shorten.component';

const routes: Routes = [
  {
    path: 'home',
    loadChildren: () =>
      import('./modules/home/<USER>').then(
        (m) => m.HomeModule
      ),
  },
  {
    path: 'category',
    loadChildren: () =>
      import('./modules/category/category.module').then(
        (m) => m.CategoryModule
      ),
  },
  {
    path: 'search',
    loadChildren: () =>
      import('./modules/search/search.module').then(
        (m) => m.SearchModule
      ),
  },
  {
    path: 'tag',
    data: { type: 'tag' },
    loadChildren: () =>
      import('./modules/search/search.module').then(
        (m) => m.SearchModule
      ),
  },
  {
    path: 'page',
    loadChildren: () =>
      import('./modules/page/page.module').then(
        (m) => m.PageModule
      ),
  },
  {
    path: 'listing',
    loadChildren: () =>
      import('./modules/listing/listing.module').then(
        (m) => m.ListingModule
      ),
  },
  {
    path: 'ads-and-add-ons',
    loadChildren: () =>
      import('./modules/ads-and-add-ons/ads-and-add-ons.module').then(
        (m) => m.AdsAndAddOnsModule
      ),
  },
  {
    path: 'advertisement',
    loadChildren: () =>
      import('./modules/advertisement/advertisement.module').then(
        (m) => m.AdvertisementModule
      ),
  },
  {
    path: 'seller',
    loadChildren: () =>
      import('./modules/seller/seller.module').then(
        (m) => m.SellerModule
      ),
  },
  {
    path: 'offers',
    loadChildren: () =>
      import('./modules/offers/offers.module').then(
        (m) => m.OffersModule
      ),
  },
  {
    path: 'notifications',
    loadChildren: () =>
      import('./modules/notification/notification.module').then(
        (m) => m.NotificationModule
      ),
  },
  {
    path: 'blank',
    component: BlankComponent,
  },
  {
    path: 'authentication',
    loadChildren: () =>
      import('./authentication/authentication.module').then(
        (m) => m.AuthenticationModule
      ),
  },
  {
    path: 'u',
    children: [
      {
        path: '',
        redirectTo: '/home', pathMatch: 'full'
      },
      {
        path: ':key',
        component: ShortenComponent,
      }
    ],
  },
  {
    path: 'go',
    children: [
      {
        path: '',
        redirectTo: '/home', pathMatch: 'full'
      },
      {
        path: ':key',
        component: ShortenComponent,
      }
    ],
  },
  {
    path: 'sitemap',
    loadChildren: () =>
      import('./modules/sitemap/sitemap.module').then(
        (m) => m.SitemapModule
      ),
  },
  { path: 'login', component: BlankComponent },
  {
    path: '404',
    loadChildren: () =>
      import('./modules/notfound/notfound.module').then(
        (m) => m.NotfoundModule
      ),
  },
  { path: '', redirectTo: '/home', pathMatch: 'full' },
  { path: '**', redirectTo: '/404', pathMatch: 'full' }
];

@NgModule({
  imports: [
    RouterModule.forRoot(routes, { scrollPositionRestoration: 'top' }),
  ],
  exports: [RouterModule],
})
export class AppRoutingModule { }
