@import "mixins";
@import "variables";

.edit-data-page {
  width: 100%;
  height: 100%;
  overflow: hidden;
  position: relative;
}

.header {
  padding: 24px;
}

.section {
  padding: 0 16px;
}
.section h3 {
  font-size: 16px;
  font-weight: 800;
  color: #272728;
  margin-bottom: 12px;
}

.profile-img {
   width: 185px;
   height: 61px;
   margin-top: -60px;
   position: relative;
   cursor: pointer;

   &.businessSize {
      width: 300px !important;
      height: 100px !important;
   }

   input {
      display: none;
   }

   .edit-icon {
      position: absolute;
      top: 10px;
      right: 10px;
      z-index: 2;
      width: 21px;
      height: 21px;
      background: white;
      border-radius: 50%;
      padding: 9px;
      display: flex;
      justify-content: center;
      align-items: center;
   }

   &.loading:after {
      content: '';
      position: absolute;
      top: 30px;
      left: 30px;
      width: 56px;
      height: 56px;
      border-radius: 50%;
      z-index: 10;
      border: 9px solid $primary;
      animation: spinner-bulqg1 0.8s infinite linear alternate,
      spinner-oaa3wk 1.6s infinite linear;
   }
   &.loading:before {
      content: '';
      position: absolute;
      top: 30px;
      left: 30px;
      width: 56px;
      height: 56px;
      border-radius: 50%;
      z-index: 9;
      background: #050505b0;
   }

}

.profile-sections {
    padding: 16px;
}
