.edit-data-page {
  width: 100%;
  height: 100%;
  background: #fff;
  overflow: hidden;
  position: relative;
}

.header {
  padding: 24px;
}
.header-content {
  display: flex;
  align-items: center;
  gap: 8px;
}
.header-content h2 {
  font-size: 16px;
  font-weight: 800;
  color: #272728;
}

/* Section */
.section {
  padding: 0 16px;
}
.section h3 {
  font-size: 16px;
  font-weight: 800;
  color: #272728;
  margin-bottom: 12px;
}

/* Cover & Logo */
.cover-wrapper {
  position: relative;
}
.cover {
  position: relative;
  border-radius: 6px;
  overflow: hidden;
}
.cover img {
  width: 100%;
  border-radius: 6px;
}
.cover .logo {
  position: absolute;
  bottom: 15px;
  left: 50%;
  transform: translateX(-50%);
  background: #f2edf7;
  border: 1px solid rgba(114,34,130,0.2);
  border-radius: 5px;
  padding: 5px;
}
.cover .logo img {
  width: 183px;
  height: 61px;
}

/* Edit Buttons */
.edit-btn {
  position: absolute;
  background: #fff;
  border-radius: 50%;
  width: 22px;
  height: 22px;
  border: none;
  cursor: pointer;
}
.edit-btn.small {
  right: 50px;
  bottom: 50px;
}
.edit-btn.top {
  right: 10px;
  top: 10px;
}
.profile-sections {
    padding: 16px;
}