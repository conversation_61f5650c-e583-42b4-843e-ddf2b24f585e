import { Component } from '@angular/core';
import { AdAddonsWidgetComponent } from '@src/app/shared/components/ad-addons-widget/ad-addons-widget.component';
import { PointsStatusWidgetComponent } from "@src/app/shared/components/points-status-widget/points-status-widget.component";
import { SharedBtnComponent } from '@src/app/shared/components/shared-btn/shared-btn.component';
import { AdsAndAddOnsService, AdsStage } from '../../services/ads-and-add-ons.service';
import { NgIf } from '@angular/common';
import { CalendarModule } from 'primeng/calendar';
import { FormBuilder, ReactiveFormsModule, Validators } from '@angular/forms';
import { SvgIconsComponent } from '@src/app/shared/components/svg-icons/svg-icons.component';
@Component({
  selector: 'app-pyament-invoice-ads-summary',
  standalone: true,
  imports: [AdAddonsWidgetComponent, PointsStatusWidgetComponent, SharedBtnComponent, NgIf, CalendarModule, ReactiveFormsModule, SvgIconsComponent],
  templateUrl: './pyament-invoice-ads-summary.component.html',
  styleUrl: './pyament-invoice-ads-summary.component.scss'
})
export class PyamentInvoiceAdsSummaryComponent {

  AdsStage = AdsStage

  constructor(public adsSvc: AdsAndAddOnsService, private fb: FormBuilder) { }

  ad = this.adsSvc.selectedAddon;

  form = this.fb.group({
    day: [new Date(), Validators.required],
    time: [new Date(), Validators.required],
  });

  onAddInvoice() {
    this.adsSvc.goTo(AdsStage.Invoice);
  }
  onAdd() {
    this.adsSvc.goTo(AdsStage.Success);
  }
}
