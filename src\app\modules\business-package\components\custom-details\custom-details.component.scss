
.plan-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.package-details {
  color: #272728;
  font-size: 14px;
}
.item {
  margin-bottom: 0px;
}
.label {
  font-weight: 700;
}

.value {
  font-weight: 400;
  display: inline;
}
.dot-icon {
  display: inline-block;
  width: 4px;
  height: 4px;
  background-color: #272728;
  margin-left: 6px;
}
.renewal-note {
  width: 100%;
  display: inline-flex;
  justify-content: flex-start;
  align-items: baseline;
  gap: 6px;
  margin-top: 0px;

  .note-text {
    font-size: 10px;
    font-weight: 700;
    color: #7C7C7C;
    line-height: 28px;
  }

  .note-icon {
    display: inline-flex;
    align-items: center;
    justify-content: center;
  }
}
li{
  list-style: none;
}