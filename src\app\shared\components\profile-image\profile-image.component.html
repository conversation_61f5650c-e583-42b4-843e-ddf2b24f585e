<div class="profile-img" [ngClass]="{
    'membership': !user.setting?.membership?.isVisable,
    'business': !!user.businessName,
    'non-business': !user.businessName
  }" *ngIf="user">
  <img [lazyload]="sellerImage"
    [placeholder]="user.businessName ? '/assets/img/avatar-business.png' : '/assets/img/avatar.png'" />
  <app-seller-memebership-icon *ngIf="user.setting"
    [membership]="user.setting.membership"></app-seller-memebership-icon>
</div>