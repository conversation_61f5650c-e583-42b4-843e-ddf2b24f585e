import { Component, EventEmitter, Input, Output } from "@angular/core";
import { SharedBtnComponent } from "@src/app/shared/components/shared-btn/shared-btn.component";
import { ViewSwitchService } from "../../services/view-switch.service";
import { DynamicHeaderComponent } from "@src/app/shared/components/dynamic-header/dynamic-header.component";
import { DynamicDialogConfig, DynamicDialogRef } from "primeng/dynamicdialog";
import { PackagesService } from "../../services/packages.service";
import { LongDatePipe } from "@src/app/shared/pipes/long-date.pipe";

@Component({
  selector: "app-cancel-confirmation",
  standalone: true,
  imports: [SharedBtnComponent, DynamicHeaderComponent, LongDatePipe],
  templateUrl: "./cancel-confirmation.component.html",
  styleUrl: "./cancel-confirmation.component.scss",
})
export class CancelConfirmationComponent {
  @Input({ required: true }) title: string = "";
  @Input() description: string = "";
  @Input() cancelDateValue: string | null = null;
  @Input() btnText: string = "";
  @Output() confirmed = new EventEmitter<void>();
  constructor(
    private view: ViewSwitchService,
    private ref: DynamicDialogRef,
    public config: DynamicDialogConfig,
    private packagesService: PackagesService
  ) {}  


  ngOnInit(): void {
    if (this.config?.data) {
      const d = this.config.data;
      this.title = d.titleText ?? this.title;
      this.description = d.description ?? this.description;
      this.cancelDateValue = d.cancelDateValue ?? this.cancelDateValue;
      this.btnText = d.btnText ?? this.btnText;
    }

    this.cancelDateValue = this.packagesService.selectedPlan().packageEndDate;
  }
  confirm() {
    this.confirmed.emit();
    this.ref.close({ confirmed: true });
  }
  goback() {
    this.view.go("business");
  }
}
