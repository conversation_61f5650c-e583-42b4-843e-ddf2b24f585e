<div class="dashboard-page">
    <div>
        <app-dynamic-header [title]="'listing details' | translate" (filterButtonClicked)="openFilter()"
            [showFilterButton]="true">

            <ng-template #headerFilter>

                <div class="filterHeader">
                    <app-svg-icons width="16px" height="16px" name="calender-icon"></app-svg-icons>
                    هذا الاسبوع
                    <app-svg-icons width="16px" height="16px" name="down-arrow-icon"></app-svg-icons>
                </div>

            </ng-template>

        </app-dynamic-header>

        <div class="ad-item" [class.ad-item--business]="isBusiness">
            <img class="ad-thumb" [src]="ads.webpImageURL" [alt]="ads.title" />

            <div class="ad-body">
                <h3 class="ad-title">{{ ads.title }}</h3>

                <a class="ad-link" href="#">
                    فتح الإعلان
                    <app-svg-icons name="back-icon" width="11px" height="11px"></app-svg-icons>
                </a>
            </div>
        </div>


        <div class="tags">
            <div class="tag-label" *ngFor="let tag of ads.tags">
                <app-svg-icons name="featured-icon" width="13px" height="13px" [color]="$any(null)"></app-svg-icons>
                <span>{{ tag }} | ينتهي بعد 2 يوم</span>
            </div>
        </div>

        <app-shared-btn *ngIf="isBusiness" label="الاضافات" (btnClick)="openAddon()" theme="noraml" size="small"
            iconName="icon-circle-plus" iconWidth="10" iconHeight="10"></app-shared-btn>

        <app-stats-block [stats]="dashboardStats" [showTitle]="false"></app-stats-block>

        <ng-container *ngIf="isBusiness">
            <div *ngFor="let chart of chartData">
                <app-chart-line-widget [data]="chart.data" [title]="chart.name"></app-chart-line-widget>
            </div>
        </ng-container>
    </div>

    <app-shared-btn *ngIf="!isBusiness" label="الاضافات" (btnClick)="openAddon()" theme="noraml" size="large"
        iconName="icon-circle-plus" iconWidth="18" iconHeight="18"></app-shared-btn>
</div>