import { Routes } from '@angular/router';
import { BlankComponent } from './components/blank/blank.component';
import { AccessGuard } from './shared/guards/access.guard';

const mainRoutes: Routes = [
    { path: '', redirectTo: 'home', pathMatch: 'full' },

    {
        path: 'home',
        loadChildren: () =>
            import('./modules/home/<USER>').then((m) => m.HomeModule),
    },
    {
        path: 'category',
        loadChildren: () =>
            import('./modules/category/category.module').then(
                (m) => m.CategoryModule
            ),

    },
    {
        path: 'search',
        loadChildren: () =>
            import('./modules/search/search.module').then(
                (m) => m.SearchModule
            ),

    },
    {
        path: 'tag',
        data: { type: 'tag' },
        loadChildren: () =>
            import('./modules/search/search.module').then(
                (m) => m.SearchModule
            ),

    },
    {
        path: 'page',
        loadChildren: () =>
            import('./modules/page/page.module').then((m) => m.PageModule),

    },
    {
        path: 'settings',
        loadChildren: () =>
            import('./modules/settings/settings.module').then((m) => m.SettingsModule),

    },
    {
        path: 'listing',
        loadChildren: () =>
            import('./modules/listing/listing.module').then(
                (m) => m.ListingModule,

            ),
        canActivate: [AccessGuard],
    },
    {
        path: 'advertisement',
        loadChildren: () =>
            import('./modules/advertisement/advertisement.module').then(
                (m) => m.AdvertisementModule
            ),

    },
    {
        path: 'ads-and-add-ons',
        loadChildren: () =>
            import('./modules/ads-and-add-ons/ads-and-add-ons.module').then(
                (m) => m.AdsAndAddOnsModule
            ),
    },
    {
        path: 'seller',
        loadChildren: () =>
            import('./modules/seller/seller.module').then(
                (m) => m.SellerModule
            ),

    },
    {
        path: 'offers',
        loadChildren: () =>
            import('./modules/offers/offers.module').then(
                (m) => m.OffersModule
            ),
        canActivate: [AccessGuard],

    },
    {
        path: 'notifications',
        loadChildren: () =>
            import('./modules/notification/notification.module').then(
                (m) => m.NotificationModule
            ),
        canActivate: [AccessGuard],

    },
    {
        path: 'authentication',
        loadChildren: () =>
            import('./authentication/authentication.module').then(
                (m) => m.AuthenticationModule
            ),

    },
    { path: 'login', component: BlankComponent },
    {
        path: 'test-event',
        loadComponent() {
            return import('./modules/page/test-event/test-event.component').then(
                (m) => m.TestEventComponent
            );
        },

    },
    {
        path: '404',
        loadChildren: () =>
            import('./modules/notfound/notfound.module').then(
                (m) => m.NotfoundModule
            ),
    },

    { path: '**', redirectTo: '404', pathMatch: 'full' },
];
export const routes: Routes = [
    ...mainRoutes,
];


