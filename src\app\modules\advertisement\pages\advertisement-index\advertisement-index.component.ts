import { Async<PERSON>ip<PERSON>, CommonModule, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, NgT<PERSON>plateOutlet } from '@angular/common';
import { Component, inject, OnInit, signal } from '@angular/core';
import { ActivatedRoute, RouterLink } from '@angular/router';
import { UntilDestroy, untilDestroyed } from '@ngneat/until-destroy';
import { Store } from '@ngrx/store';
import { ConversionEvents } from '@src/app/shared/constants/conversion-events';
import { IsAuthorizedDirective } from '@src/app/shared/directives/isauthorized.directive';
import { AdsRoutes, StripAdsType } from '@src/app/shared/models/lookup.model';
import { ForAdoptionPipe } from '@src/app/shared/pipes/foradoption.pipe';
import { LinkParserPipe } from '@src/app/shared/pipes/linkparser.pipe';
import { ListingImagePathPipe } from '@src/app/shared/pipes/listing-image-path.pipe';
import { SlugTagsPipe } from '@src/app/shared/pipes/slug-tags.pipe';
import { AdsService } from '@src/app/shared/services/ads.service';
import { LogsService } from '@src/app/shared/services/consolelogs/logs.service';
import { MenuItem } from 'primeng/api';
import { ButtonModule } from 'primeng/button';
import { DialogModule } from 'primeng/dialog';
import { catchError, map, Observable, tap, throwError } from 'rxjs';
import { User } from 'src/app/authentication/models/dto/user.dto.model';
import { AdvertisementService } from 'src/app/modules/advertisement/services/advertisement.service';
import { AlertHandlerService } from 'src/app/modules/core/alerts/alert-handler.service';
import { BrowserService } from 'src/app/modules/core/service/browser.service';
import { MetaService } from 'src/app/modules/core/service/meta.service';
import { TranslationService } from 'src/app/modules/core/service/translation.service';
import { ListingDetails, ListingPaymentMethod, ListingStatus } from 'src/app/shared/models/listing.model';
import { PreviewModel } from 'src/app/shared/models/preview.model';
import { AuthService } from 'src/app/shared/services/auth.service';
import { CommonService } from 'src/app/shared/services/common.service';
import { DeviceDetectionService } from 'src/app/shared/services/device-detection.service';
import { ListingService } from 'src/app/shared/services/listing.service';
import { LookupService } from 'src/app/shared/services/lookup.service';
import { PreviewService } from 'src/app/shared/services/preview.service';
import { getAds, getCategories, getUser } from 'src/app/store/app/selectors/app.selector';
import { GrayBtnComponent } from '../../../../shared/components/gray-btn/gray-btn.component';
import { ItemCarouselComponent } from '../../../../shared/components/item-carousel/item-carousel.component';
import { ItemsSliderComponent } from '../../../../shared/components/items-slider/items-slider.component';
import { CarouselItem, ListingCarouselComponent } from '../../../../shared/components/listing-carousel/listing-carousel.component';
import { SecondaryBtnComponent } from '../../../../shared/components/secondary-btn/secondary-btn.component';
import { ItemDetailsSkeletonComponent } from '../../../../shared/components/skeleton/item-details-skeleton/item-details-skeleton.component';
import { SwappableItemsComponent } from '../../../../shared/components/swappable-items/swappable-items.component';
import { VerifiedClickDirective } from '../../../../shared/directives/isverifiedclick.directive';
import { FromNowPipe } from '../../../../shared/pipes/from-now.pipe';
import { NCurrencyPipe } from '../../../../shared/pipes/ncurrency.pipe';
import { NtranslatePipe } from '../../../../shared/pipes/ntranslate.pipe';
import { SlugListingPipe } from '../../../../shared/pipes/slug-listing.pipe';
import { AdCheckofferBtnComponent } from '../../components/ad-checkoffer-btn/ad-checkoffer-btn.component';
import { AdCommentsComponent } from '../../components/ad-comments/ad-comments.component';
import { AdSellerInfoComponent } from '../../components/ad-seller-info/ad-seller-info.component';
import { AdStatusViewComponent } from '../../components/ad-status-view/ad-status-view.component';
import { AdSwappOptionsComponent } from '../../components/ad-swapp-options/ad-swapp-options.component';
import { AdTopLinksComponent } from '../../components/ad-top-links/ad-top-links.component';
import { ListingInstructionsComponent } from '../../components/listing-instructions/listing-instructions.component';
import { ListingOffersComponent } from '../../components/listing-offers/listing-offers.component';
import { HideListingPipe } from '../../pipe/hidelisting.pipe';
import { IsViewModePipe } from '../../pipe/isviewmode.pipe';
import { ListingImagesUrlPipe } from '../../pipe/listing-images-url.pipe';



@UntilDestroy({ checkProperties: true })
@Component({
  selector: 'app-advertisement-index',
  templateUrl: './advertisement-index.component.html',
  styleUrls: ['./advertisement-index.component.scss'],
  providers: [ListingImagePathPipe, SlugListingPipe],
  standalone: true,
  imports: [
    CommonModule,
    AdTopLinksComponent,
    ItemCarouselComponent,
    ListingCarouselComponent,
    NgTemplateOutlet,
    NgClass,
    NgFor,
    RouterLink,
    AdSellerInfoComponent,
    AdCommentsComponent,
    ListingInstructionsComponent,
    ListingOffersComponent,
    ItemsSliderComponent,
    SwappableItemsComponent,
    AdCheckofferBtnComponent,
    AdSwappOptionsComponent,
    AdStatusViewComponent,
    GrayBtnComponent,
    VerifiedClickDirective,
    SecondaryBtnComponent,
    DialogModule,
    ItemDetailsSkeletonComponent,
    AsyncPipe,
    NCurrencyPipe,
    FromNowPipe,
    NtranslatePipe,
    HideListingPipe,
    ListingImagesUrlPipe,
    IsViewModePipe,
    LinkParserPipe,
    ForAdoptionPipe,
    ButtonModule,
    IsAuthorizedDirective,
    SlugTagsPipe
  ],
})
export class AdvertisementIndexComponent implements OnInit {
  listingID: number;
  urls: any[] = [];
  carouselItems: CarouselItem[] = [];
  items: string[] = [];
  listing$: Observable<ListingDetails | null>;
  currentListing: ListingDetails | null = null;
  userListings: PreviewModel[];
  recomended: PreviewModel[];
  offerMenuVisible: boolean = false;
  hasOffer: boolean = false;
  viewMode: boolean = false;
  currentUser: User;
  recommendedlisting$: Observable<PreviewModel[]>;

  category: MenuItem;

  ListingStatus = ListingStatus;

  ListingPaymentMethod = ListingPaymentMethod;

  totalBooleanProps: number = 0;

  thereIsMore: boolean = false;

  notFoundSignal = signal(false);

  logger = inject(LogsService);

  constructor(
    protected _authService: AuthService,
    private _listingService: ListingService,
    private _previewService: PreviewService,
    public device: DeviceDetectionService,
    private activatedRoute: ActivatedRoute,
    private store: Store,
    private alertModel: AlertHandlerService,
    private translateService: TranslationService,
    private metaService: MetaService,
    private advertisementService: AdvertisementService,
    private _lookupService: LookupService,
    private commonService: CommonService,
    private browser: BrowserService,
    private lp: ListingImagePathPipe,
    private sp: SlugListingPipe,
    private adsService: AdsService,

  ) {

  }

  descriptionClicked(e) {
    if (e.target.classList.contains('phone-placeholder') && this.currentUser) {
      const phone = e.target.getAttribute('data-phone');
      if (phone && !this.viewMode) this.callCustomer(phone);
    }
  }

  activeListingAux() {
    this.store.select(getUser).pipe(untilDestroyed(this)).subscribe(res => {
      if (!res) return;
      this.currentUser = res;
    });

    this.adsService.setAdsRoute(AdsRoutes.MyListings);
    this.adsService.setAdsStripType(StripAdsType.MyListing);

  }

  backToMyListings() {
    if (this.browser.isBrowser()) {
      window.history.back();
    }

  }

  ngOnInit(): void {



    this.activatedRoute.data.subscribe((data) => {
      this.viewMode = data['view'];
      this.activeListingAux();
    });


    this.activatedRoute.params.subscribe((params) => {
      const idParam = params['id'];
      if (idParam) {
        const lisitngId = idParam.match(/\d+/);
        this.listingID = Number(lisitngId ? lisitngId[0] : 0);

        if (this.listingID) {
          if (this.viewMode) {
            if (this.browser.isBrowser()) {
              this.getListing();
            }
          } else {
            this.getListing();
          }
        }

      }
    });

  }



  resetValues() {
    this.advertisementService.setStep(1);
  }


  scrollToComments() {
    this._listingService.scrollToComment();
  }

  hasFeatures(p) {
    return p.filter(item => item.propTypeName == 'Boolean' && item.value != '').length > 0;
  }

  getTagName(name) {
    return this.translateService.instant(name);
  }

  getListing() {

    this.listing$ = this._listingService.getListingDetails(this.listingID).pipe(map(res => res.data), tap(res => {

      this.currentListing = res;

      if (res.userID !== this.currentUser?.userID && this.viewMode) {
        window.location.href = this.commonService.getSiteURL() + '/advertisement/' + res.listingID + '-' + encodeURIComponent(this.sp.transform(res));
      }


      if (res.metaTag) {
        this.metaService.set({
          title: res.compositeName ?? (res.metaTag!.title!.length > 0 ? res.metaTag.title : res.name),
          description: res.metaTag!.description!.length > 0 ? res.metaTag.description : res.description,
          image: this.lp.transform(res.imageURL, 'main'),
          url: this.commonService.getSiteURL() + '/advertisement/' + res.listingID + '-' + encodeURIComponent(this.sp.transform(res)),
          keywords: res.metaTag.keywords
        });

      } else {
        this.metaService.set({
          title: res.compositeName ?? res.name,
          description: res.description,
          image: this.lp.transform(res.imageURL, 'main'),
          url: this.commonService.getSiteURL() + '/advertisement/' + res.listingID + '-' + encodeURIComponent(this.sp.transform(res)),
        });
      }
      if (!this.viewMode) {
        this.commonService.pushDataLayer({
          event: ConversionEvents.Listing_View,
          listing_id: res?.listingID,
          listing_name: res.name,
          category_name: res?.categoryName,
        });
        this.commonService.pushDataLayer({ ecommerce: null });
        this.commonService.pushDataLayer({
          event: "view_item",
          listing_id: res.listingID,
          ecommerce: {
            currency: "EGP",
            value: res.price,
            items: [
              {
                item_id: res.listingID,
                item_name: res.name,
                index: 0,
                item_category: res.categoryName,
                price: res.price,
              }
            ]
          }
        });

      }


      this.totalBooleanProps = res.properties!.reduce((count, currentItem) => {
        return currentItem.propTypeName === 'Boolean' ? count + 1 : count;
      }, 0);

      if (this.totalBooleanProps > 4) {
        this.thereIsMore = true;
      }



      this.urls = res.images.map((y) => ({
        id: y.imageID,
        url: y.imageURL,
      }));

      // Convert URLs to CarouselItem format for the new carousel
      this.carouselItems = res.images.map((y) => ({
        type: 'image' as const,
        data: {
          imageUrl: y.imageURL,

        }
      }));

      this.store.select(getAds).pipe(map(e => e.find(item => item.locationName == 'CarouselMpu'))).subscribe((ad) => {
        if (ad) {
          this.insertCarouselAds(res.views, ad);
        }
      });

      this.store.select(getCategories).pipe(
        untilDestroyed(this),
        map(response => response.menu),
        map(response => this._lookupService.convertToMenuItems(response)),
        tap(response => {
          const { category, parent } = this._lookupService.findCategoryById(res.categoryID, response);
          this.category = category;
        })
      ).subscribe();


      const coverIndex = this.urls.findIndex((item) => item.url === res.imageURL);
      if (coverIndex !== -1) {
        const coverItem = this.urls.splice(coverIndex, 1)[0];
        this.urls.unshift(coverItem);
      }



      this.recommendedlisting$ = this._previewService.getRecommededListing(res.categoryID, res.listingID).pipe(map(res => res.body!.data));





    }),
      catchError(error => {
        this.logger.log("listing error", error);
        if (error.status == 404) {
          this.metaService.addNoIndex();
          this.notFoundSignal.set(true);
        }

        return throwError(() => error);
      })
    );
  }

  get forcePhoneVisible() {
    return this.advertisementService.sellerInfo?.forcePhoneVisibility;
  }


  callCustomer(phoneNumber?: string) {
    this.commonService.pushDataLayer({
      event: ConversionEvents.Contact_View,
      listing_id: this.listingID,
      contact_type: 'Phone',
      event_location: 'Listing_Details',
      seller_id: this.currentListing.userID.toString(),
      listing_name: this.currentListing?.name,
      category_name: this.currentListing?.categoryName,

    });
    this.alertModel.call({ phone: phoneNumber ? phoneNumber : this.advertisementService.sellerInfo.phoneNumber }, (e) => {
      if (e) {
        const telUri = `tel:${phoneNumber ? phoneNumber : this.advertisementService.sellerInfo.phoneNumber}`;
        window.location.href = telUri;
        this.commonService.pushDataLayer({
          event: ConversionEvents.Click_To_Call,
          listing_id: this.listingID,
          title: 'Call',
          event_location: 'Listing_Details',
          seller_id: this.currentUser.userID,
          listing_name: this.currentListing?.name,
          category_name: this.currentListing?.categoryName,
        });
        this.commonService.sendEvent('call_button_clicked', 'Call', this.listingID);

      }
    });

  }

  sendEvent(event: Event) {
    this.commonService.sendEvent('call_button_clicked_unlogged', 'Call', this.listingID);

  }

  canViewPhone() {
    return this.advertisementService.sellerInfo?.forcePhoneVisibility || this.advertisementService.sellerInfo?.phoneVisible;
  }

  trackby(index, item) {
    return item.label;
  }
  trackbyName(index, item) {
    return item.name;
  }






  getRecommendedListings(res: ListingDetails) {


  }



  private insertCarouselAds(views: number, ad: any): void {
    if (!ad || !ad.configurations) return;

    const configs = ad.configurations.reduce((acc, config) => {
      acc[config.configKey] = config.configValue;
      return acc;
    }, {} as Record<string, string>);

    const adblockPositions = configs['adblock_positions'];
    const listingViews = configs['min_listing_views_count'];

    if (!adblockPositions && listingViews >= views) {
      this.carouselItems.push({
        type: 'ads' as const,
        ads: ad
      });
      return;
    }

    const positions = adblockPositions
      .split(',')
      .map(pos => parseInt(pos.trim(), 10) - 1)
      .filter(index => index >= 0)
      .sort((a, b) => a - b);

    positions.forEach(index => {
      const insertIndex = Math.min(index, this.carouselItems.length);
      this.carouselItems.splice(insertIndex, 0, {
        type: 'ads' as const,
        ads: ad
      });
    });
  }



}
