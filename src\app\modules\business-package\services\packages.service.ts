import { HttpClient } from "@angular/common/http";
import { computed, Injectable, signal } from "@angular/core";
import { environment } from 'src/environments/environment';
import { PackagePlan } from "../models/packages.model";

@Injectable({
    providedIn: 'root'
})

export class PackagesService {

    constructor(private http: HttpClient) {}
    private readonly _plans = signal<PackagePlan[] | null>(null);
    public readonly plans = this._plans.asReadonly();
    readonly selectedPlanId = signal<number | null>(null);
    readonly selectedPlan = computed(() => this._plans()?.find(p => p.id === this.selectedPlanId()) ?? null);

    planById = (id: number) => computed(() => this._plans()?.find(p => p.id === id));

    getPackagePlans() {
        return this.http.get<PackagePlan[]>(`${environment.authAPI}/api/Subscription/plans`);
    }

    setPlans(plans: PackagePlan[]) {
        this._plans.set(plans);
    }

    selectPlan(id: number) {
    this.selectedPlanId.set(id);
    }

}