import { CommonModule } from '@angular/common';
import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { FormBuilder, FormGroup, FormsModule, ReactiveFormsModule, UntypedFormControl } from '@angular/forms';
import { HeicConversionService } from '@src/app/core/services/heic.service';
import { BrowserService } from '@src/app/modules/core/service/browser.service';
import { BusinessProfileImageComponent } from '@src/app/modules/seller/components/business-profile-image/business-profile-image.component';
import { ProfileModel } from '@src/app/shared/models/profile.model';
import { AppcenterService } from '@src/app/shared/services/appcenter.service';
import { DialogModule } from 'primeng/dialog';
import { Observable, map, tap } from 'rxjs';
import { AlertHandlerService } from 'src/app/modules/core/alerts/alert-handler.service';
import { TranslationService } from 'src/app/modules/core/service/translation.service';
import { FormInterestControlComponent } from 'src/app/modules/listing/components/form-interest-control/form-interest-control.component';
import { SellerProfileImageComponent } from 'src/app/modules/seller/components/seller-profile-image/seller-profile-image.component';
import { DarkBtnComponent } from 'src/app/shared/components/dark-btn/dark-btn.component';
import { WhiteBtnComponent } from 'src/app/shared/components/white-btn/white-btn.component';
import { UserIntrests } from 'src/app/shared/models/userintrests.model';
import { NtranslatePipe } from 'src/app/shared/pipes/ntranslate.pipe';
import { RoundPipe } from 'src/app/shared/pipes/round.pipe';
import { AuthService } from 'src/app/shared/services/auth.service';
import { ProfileService } from 'src/app/shared/services/profile.service';
import { InterestService } from 'src/app/shared/services/userintrests.service';
import { environment } from 'src/environments/environment';
import { FormUtils } from 'src/utils/form-utils';
import { SharedModule } from "@src/app/shared/shared.module";

@Component({
  selector: 'app-profile-header',
  standalone: true,
  imports: [CommonModule, DialogModule, NtranslatePipe, RoundPipe, SellerProfileImageComponent, FormInterestControlComponent, DialogModule, FormsModule, ReactiveFormsModule, WhiteBtnComponent, DarkBtnComponent, BusinessProfileImageComponent, SharedModule],
  templateUrl: './profile-header.component.html',
  styleUrls: ['./profile-header.component.scss']
})
export class ProfileHeaderComponent implements OnInit {

  @Output() onEdit = new EventEmitter();
  @Input() user: ProfileModel;
  imageUrl: string;
  fileToUpload: any;
  ratingModelVisible: boolean = false;
  loading: boolean = false;
  maxSize = 5 * 1024 * 1024;

  maxWidth = 1000;
  modelVisible = false;
  interestForm: FormGroup;

  interests$: Observable<UserIntrests[]>;

  originalInterest = [];

  referralLink = '';



  constructor(
    private _profileService: ProfileService,
    private _authService: AuthService,
    private alertService: AlertHandlerService,
    private translateService: TranslationService,
    private interestService: InterestService,
    private fb: FormBuilder,
    private alertModal: AlertHandlerService,
    private languageService: AppcenterService,
    private browser: BrowserService,
    private hcs: HeicConversionService

  ) { }

  ngOnInit(): void {

    this.referralLink = `${environment.clintURL}/${this.languageService.lang}/authentication/register/${this.user.referralCode}`;

    this.interestForm = this.fb.group({
      interestIds: new UntypedFormControl(null),
    });


    this.getInterests();

  }

  resetValues() {
    this.interestForm.patchValue({
      interestIds: this.originalInterest
    });
  }

  openInterestPopup() {
    this.modelVisible = true;
  }

  saveInterest() {
    this.modelVisible = false;
    this.interestService.editUserIntrest({
      categories: this.interestForm.get('interestIds')!.value ? this.interestForm.get('interestIds')!.value : []
    }).subscribe(res => {
      this.alertModal.success({
        message: this.translateService.instant("ProfileSavedInterest"),
        buttons: [
          { title: this.translateService.instant("Continue"), value: true },
        ]
      }, (res => {
        location.reload();
      }));
    });
  }

  copyLink() {

    if (this.browser.isBrowser()) {
      const tempInput = document.createElement('input');
      tempInput.value = this.referralLink;
      document.body.appendChild(tempInput);
      tempInput.select();
      document.execCommand('copy');
      document.body.removeChild(tempInput);
      this.alertModal.success({
        message: this.translateService.instant("ReferralCodeCopied"),
        buttons: [
          { title: this.translateService.instant("Continue"), value: true },
        ]
      });
    }
  }

  closeInterest() {
    this.modelVisible = false;
  }

  saveImage() {
    if (this.fileToUpload) {
      this.loading = true;
      let uploadData = new FormData();

      uploadData.append(
        'userprofile',
        this.fileToUpload.blob,
        this.fileToUpload.name
      );
      uploadData.append('id', this.user.userID!.toString());
      this._profileService
        .uploadImgs(uploadData).subscribe(y => {
          let user = { ...this._authService.userValue };
          user.userImage = y?.body?.imageURL!;
          this.user = { ...this.user, userImage: user.userImage };
          this.loading = false;
          this._authService.sendUserChanged(user);
          this.onEdit.emit(true);
        });
    }
  }

  getInterests() {
    this.interests$ = this.interestService.getUserInterest().pipe(map(res => res?.data), tap(res => {
      this.interestForm.patchValue({
        interestIds: res.map(item => item.categoryID)
      });

      this.originalInterest = this.interestForm.get('interestIds')!.value;
    }));
  }


  get isBusiness(): boolean {
    return this.user && this.user.account && (this.user.account.accountType == 'business');
  }

  async handleFileInput($event: any) {

    if (this.browser.isBrowser()) {

      let file = $event.target.files;

      const fileImg = file[0];


      let convertedFile;

      if (!FormUtils.validImageFile(fileImg)) {
        this.alertService.warn({ message: this.translateService.instant('Invalid_Extension') });
        return;
      }

      var validHic = FormUtils.validHicFile(fileImg);

      if (validHic) {
        convertedFile = await this.hcs.convertIfHeic(fileImg);
      }

      this.resizeAndConvertImage(validHic ? convertedFile : fileImg, (event, blob) => {

        this.fileToUpload = {
          name: validHic ? convertedFile.name : fileImg.name,
          blob
        };
        this.user.imageURL = event.target.result;
        this.saveImage();
      });

    }


  }

  getRates() {
    this.alertService.userRatings({ message: this.user.userID });
  }

  resizeAndConvertImage(file: File, callback: (e: any, blob: Blob) => void) {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = (event: any) => {
      const img = new Image();
      img.src = event.target.result;
      img.onload = () => {
        const canvas = document.createElement('canvas');
        const scaleSize = this.maxWidth / img.width;
        canvas.width = this.maxWidth;
        canvas.height = img.height * scaleSize;

        const ctx = canvas.getContext('2d');
        ctx!.drawImage(img, 0, 0, canvas.width, canvas.height);

        ((ee) => {
          canvas.toBlob(blob => {
            callback(ee, blob!);
          }, 'image/jpeg');

        })(event);
      };
    };
  }

}
