<div [formGroup]="contactForm">
    <div class="form_fields">
        <label>{{ 'ListingContactName' | translate }}</label>
        <input formControlName="listingContactName" />
        <div class="form_errors">
            <div *ngFor="let error of contactForm.get('listingContactName')!.errors | keyvalue">
                {{ error.value | translate}}
            </div>
        </div>
    </div>
    <div class="form_fields">
        <label>{{ 'ListingContactPhone' | translate }}</label>
        <input formControlName="listingContactPhone" />
        <div class="form_errors">
            <div *ngIf="contactForm.get('listingContactPhone').hasError('pattern')">
                {{ 'InvalidPhoneNumber' | translate }}
            </div>
            <div *ngIf="contactForm.get('listingContactPhone').hasError('minlength')">
                {{ 'InvalidPhoneNumber' | translate }}
            </div>
        </div>
    </div>
</div>