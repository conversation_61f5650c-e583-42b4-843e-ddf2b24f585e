body {
  --sb-track-color: #eeeef1;
  --sb-track-color-light: #e9e7e7;
  --sb-thumb-color: var(--secondary-color);
  --sb-thumb-color-light: #dab2ff;
  --sb-size: 5px;
  --sb-size-light: 3px;
  --side-menu-top-pos: 58px;


  * {

    ::-webkit-scrollbar {
      width: var(--sb-size-light);
    }

    ::-webkit-scrollbar-track {
      background: var(--sb-track-color-light);
      border-radius: 3px;
      box-shadow: none;
    }

    ::-webkit-scrollbar-thumb {
      background: var(--sb-thumb-color-light);
      border-radius: 3px;

    }

  }
}

body::-webkit-scrollbar {
  width: var(--sb-size);
}

body::-webkit-scrollbar-track {
  background: var(--sb-track-color);
  border-radius: 3px;
}

body::-webkit-scrollbar-thumb {
  background: var(--sb-thumb-color);
  border-radius: 3px;

}

@supports not selector(::-webkit-scrollbar) {
  body {
    scrollbar-color: var(--sb-thumb-color) var(--sb-track-color);
  }
}

a {
  text-decoration: none;
}

.is-login {
  main {
    flex: 0 0 auto;
    width: 100%;
    padding: 0 !important;
  }
}

html,
body {
  height: 100%;
  background-color: $secondary;

  // @include media-breakpoint-down(lg) {
  //   font-size: 14px;
  // }

  // @include media-breakpoint-down(md) {
  //   font-size: 12px;
  // }

  --bs-body-font-family: 'Cairo', sans-serif !important;
  --font-family: 'Cairo', sans-serif !important;
  font-family: $fontEn, sans-serif !important;

}

html {
  font-size: 0.85rem;
  font-size: clamp(0.85rem, 0.65rem + 0.45vw, 1rem);
}

:root {
  --arrow-rotation: 0deg;
  --arrow-rotation-opposite: 180deg;
}

.row {
  margin: 0px !important;
}

.dark-theme {
  --primary-color: darkblue;
}


html[lang=ar] {
  --arrow-rotation: 180deg;
  --arrow-rotation-opposite: 0deg;

  body {



    .p-button-label {
      font-weight: 400;
    }



  }
}




// .row > * {
//   padding-right: 0px;
//   padding-left: 0px;
// }
.back_btn {
  background: transparent;
  border: none;
  color: #000;
  stroke: #2D3142;
  opacity: 0.4;
}

.back_btn .pi {
  font-size: 1.5rem;
}

@media (min-width: 1200px) {

  .container-xl,
  .container-lg,
  .container-md,
  .container-sm,
  .container {
    max-width: clamp(1140px, 92vw + 1rem, 1440px);
  }
}


.md-drppicker {
  opacity: 0;
  transform: scale(1) !important;
  @include ltr(right, 0 !important);
  @include rtl(left, 0 !important);
  @include ltr(left, auto !important);
  @include rtl(right, auto !important);
  transition: opacity 0.3s !important;

  .ranges {
    li:last-child {
      border-top: 1px solid #cbcbd9;
    }
  }

  .btn {
    background-color: $primary !important;
    text-transform: capitalize;
    font-size: 0.85rem !important;
    padding: 0 0.75rem !important;
    line-height: 29px !important;
    font-family: $f-r !important;
  }
}

.range-picker {
  position: relative;

  .ranges {
    width: 100% !important;
    min-width: 8rem;
  }
}

.md-drppicker.shown {
  // right: 3rem !important;
  // left: auto !important;
  // opacity: 1;
  @include ltr(right, 0 !important);
  @include rtl(left, 0 !important);
  @include ltr(left, auto !important);
  @include rtl(right, auto !important);
  opacity: 1;
  top: 48px !important;
  width: 100%;
  display: flex;
  align-items: flex-end;
}

:focus-visible {
  outline: 0;
}

.md-drppicker .ranges ul li button.active {
  background-color: $side-nav-link-active-color !important;
  color: #2d3142 !important;
}

.md-drppicker td.active,
.md-drppicker td.active:hover {
  background-color: $primary !important;
}



.btn-light {
  color: $primary !important;
}

table td {
  a {
    cursor: pointer;
  }
}

a,
button,
.icon-cursor {
  &:hover {
    cursor: pointer;
  }
}

.login {
  min-height: 100vh;
}

.bg-image {
  background-color: $grey-1;
  // min-height: 100vh;
  height: 100vh;

  @media (max-width: 991.98px) {
    min-height: auto;

    .inner {
      height: 25vh !important;
    }
  }

  .inner {
    background-image: url("/assets/img/login-artwork.png");
    height: 100%;
    background-position: center center;
    background-size: contain;
    background-repeat: no-repeat;
  }
}

.info-side {
  height: 100vh;
  //   overflow-y: scroll;
  overflow-y: auto;
}

.login-heading {
  font-weight: 300;
}

.btn-login {
  font-size: 0.9rem;
  letter-spacing: 0.05rem;
  padding: 0.75rem 1rem;
  border-radius: 28px;
}

hr:not([size]) {
  height: 0.1rem;
  background-color: #858585;
}

hr:not([size]).light {
  height: 0.1rem;
  background-color: #a8a8aa;
}

hr:not([size]).light2 {
  height: 0.1rem;
  background-color: #e3e3e7;
}



.add-item {
  position: relative;

  .inner {
    width: 10rem;
    height: 10rem;
    background-color: $primary;
    display: flex;
    align-items: center;
    justify-content: center;
    @include ltr(margin-right, 1rem);
    @include rtl(margin-left, 1rem);
    border-radius: 0.5rem;
    margin-bottom: 1rem;
  }

  .icon {
    background-color: #8a51be;
    border-radius: 50%;
    font-size: 2rem;
    color: #ffffff;
    padding: 1rem;
  }
}

ul.numbered-list {
  counter-reset: li;
  list-style-type: none;
  font-size: 14px;
  line-height: 18px;
  @include ltr(padding-left, 0);
  @include rtl(padding-right, 0);

  li {
    position: relative;
    padding: 5px 0 5px 30px;

    &:before {
      content: counter(li);
      counter-increment: li;
      height: 20px;
      width: 20px;
      border: 1px solid $side-nav-link-active-color;
      background-color: $side-nav-link-active-color;
      border-radius: 50%;
      color: $primary;
      text-align: center;
      position: absolute;
      @include ltr(left, 0);
      @include rtl(right, 0);
      top: 4px;
    }
  }
}

.offer-card {
  img {
    height: 12rem;
  }

  border: 0;

  .card-body {
    box-shadow: 0 2px 0 0 rgba(172, 172, 172, 0.15);
    border: solid 1px rgba(235, 235, 235, 0.43);
    padding: 0.5rem;
  }
}

.add-item {
  margin-bottom: 1.35rem;
  cursor: pointer;
  position: relative;

  .header {
    position: relative;
    // background-image: url('../../../../assets/img/add-item.png');
    background-color: #f7f7fb;
    background-size: cover;
    background-position: center center;
    height: 14rem;
    border-top-left-radius: 0.5rem;
    border-top-right-radius: 0.5rem;

    img {
      object-fit: cover;
      max-height: 100%;
      width: 100%;
      border-top-left-radius: 0.5rem;
      border-top-right-radius: 0.5rem;
    }
  }

  .menu {
    position: absolute;
    top: 0;
    @include ltr(right, 0);
    @include rtl(left, 0);
    width: 3rem;
    height: 3rem;
    @include ltr(text-align, right);
    @include rtl(text-align, left);
    padding: 1rem 1rem 0 0;
    z-index: 1;
    color: #ffffff;
  }

  .footer {
    border-bottom-left-radius: 0.5rem;
    border-bottom-right-radius: 0.5rem;
    box-shadow: 0 2px 0 0 rgba(172, 172, 172, 0.15);
    border: solid 1px rgba(235, 235, 235, 0.43);
    padding: 1rem;
  }
}



app-top-nav .lang-toggle {
  width: auto;

  @media (max-width: 767.98px) {
    // position: static;
  }
}

app-footer .mt-5 {
  margin-top: 3rem !important;
}

// .sidenav-open .dir-container[dir=ltr] .lang-toggle {
//     right: 1rem
// }

// .sidenav-open .dir-container[dir=rtl] .lang-toggle {
//     left: 1rem;
// }

.btn {
  white-space: nowrap;
}

@keyframes shine {
  to {
    background-position-x: -200%;
  }
}

.placeholder-glow {
  border: 0 !important;

  .placeholder {
    // opacity: .3;

    background: #eee;
    background: linear-gradient(110deg, #ececec 8%, #f5f5f5 18%, #ececec 33%);
    border-radius: 5px;
    background-size: 200% 100%;
    animation: 1.5s shine linear infinite;
    opacity: 1;
  }
}

.card-body {
  padding: 2rem 2rem;
}

.zero-state {
  img {
    @media (max-width: 767.98px) {
      max-width: 100%;
      height: auto;
    }
  }
}

ng-confirm .modal-overlay {
  display: flex;
  align-items: center;
  justify-content: center;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 5;
  background-color: #0009;
  opacity: 0;
  visibility: hidden;
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
  transition: opacity 0.6s cubic-bezier(0.55, 0, 0.1, 1),
    visibility 0.6s cubic-bezier(0.55, 0, 0.1, 1);

  .modal1 {
    background-color: #fff;
    border-radius: 7px;
    -webkit-border-radius: 7px;
    -moz-border-radius: 7px;
    -ms-border-radius: 7px;
    -o-border-radius: 7px;

    .close-modal {
      svg {
        width: 20px !important;
        height: 20px !important;
      }
    }

    .modal-content1 {
      h1 {
        font-size: 24px;
        font-weight: 600;
        letter-spacing: 0.34px;
        color: #2d3142;
        font-family: $f-r;
        text-align: center;
      }

      .dialog-content {
        font-size: 18px;
        font-weight: 400;
        letter-spacing: 0.34px;
        color: #52556a;
        font-family: $f-r;
        text-align: center;
      }

      .dialog-footer {
        width: 100%;
        justify-content: center;
        right: 50%;
        margin-top: 16px;
        gap: 24px;
        flex-direction: row-reverse;
        transform: translateX(50%);
        -webkit-transform: translateX(50%);
        -moz-transform: translateX(50%);
        -ms-transform: translateX(50%);
        -o-transform: translateX(50%);

      }
    }
  }
}

::-webkit-scrollbar {
  width: 4px;
  height: 3px;
}

::-webkit-scrollbar-track {
  -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
  box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
  background-color: #eee;
  border-radius: 10px;
  -webkit-border-radius: 10px;
  -moz-border-radius: 10px;
  -ms-border-radius: 10px;
  -o-border-radius: 10px;
}

::-webkit-scrollbar-thumb {
  background-color: #aaa;
  outline: 1px solid #eee;
  border-radius: 10px;
  -webkit-border-radius: 10px;
  -moz-border-radius: 10px;
  -ms-border-radius: 10px;
  -o-border-radius: 10px;
}