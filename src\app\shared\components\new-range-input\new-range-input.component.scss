
.ads-range{
  width: 100%;
  color:#272728;
}

.ads-range__header{
  font-weight:800;
  font-size:16px;
  line-height:1;
  margin-bottom:6px;
}

.ads-range__row{
  display:flex;
  align-items:center;
  gap:10px;
}

.ads-range__value,
.ads-range__min{
  direction: ltr;
  font-size:12px;
  font-weight:700;
  color:#7C7C7C;
  text-align:start;  
}

.ads-range__track{
  flex:1;
  display:flex;
  align-items:center;
}

#adsRange{
  appearance:none;
  width:100%;
  height:6px;
  background: linear-gradient(to left, #FAAF40 calc(var(--val, .5)*100%), #D9D9D9 0);
  border-radius:999px;
  outline:none;
  cursor:pointer;
}

#adsRange::-webkit-slider-thumb{
  appearance:none;
  width:18px;
  height:18px;
  border-radius:50%;
  background: #FAAF40;
  margin-top:-6px;              
}

#adsRange::-moz-range-track{
  height:6px;
  background: transparent;       
}
#adsRange::-moz-range-progress{
  height:6px;
  background: #FAAF40;
  border-radius:999px 0 0 999px;
}
#adsRange::-moz-range-thumb{
  width:18px;
  height:18px;
  border-radius:50%;
  background:#FAAF40;
  border: 2px solid #fff;
  box-shadow: 0 0 0 2px rgba(0,0,0,.06);
}

#adsRange:focus-visible{
  outline: none;
  box-shadow: 0 0 0 3px rgba(250,175,64,.25);
  border-radius: 999px;
}
