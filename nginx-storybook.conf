server {
    listen       6006;

    gzip on;                                                                            
    gzip_disable "msie6";                                                                                                                                         
    gzip_vary on;                                                                       
    gzip_proxied any;                                                                   
    gzip_comp_level 6;                                                                  
    gzip_buffers 16 8k;                                                                 
    gzip_http_version 1.1;                                                              
    gzip_min_length 256;                                                                
    gzip_types                                                                          
    application/atom+xml                                                              
    application/geo+json                                                              
    application/javascript                                                            
    application/x-javascript                                                          
    application/json                                                                  
    application/ld+json                                                               
    application/manifest+json                                                         
    application/rdf+xml                                                               
    application/rss+xml                                                               
    application/xhtml+xml                                                             
    application/xml                                                                   
    font/eot                                                                          
    font/otf                                                                          
    font/ttf                                                                          
    image/svg+xml                                                                     
    text/css                                                                          
    text/javascript                                                                   
    text/plain                                                                        
    text/xml;

    location / {
        root /usr/share/nginx/html;
        add_header Set-Cookie "Path=/; HttpOnly; Secure;";
        proxy_cookie_path / "/; secure";
        try_files $uri $uri/ /index.html;

    }

    location ~* \.(?:ico|css|js|gif|jpe?g|png|woff|woff2|svg|)$ {                      
        root /usr/share/nginx/html;
        add_header Set-Cookie "Path=/; HttpOnly; Secure;";
        proxy_cookie_path / "/; secure";
        try_files $uri $uri/ /index.html;
        expires 365d;                                                                   
        add_header Pragma public;                                                       
        add_header Cache-Control "public";
    }
}
