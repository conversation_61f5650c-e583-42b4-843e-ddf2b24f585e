import { Meta, StoryObj, moduleMetadata } from "@storybook/angular";
import { PackageDetailsComponent } from "./package-details.component";
import { PackagesService } from "../../services/packages.service";
import { PackagePlan } from "../../models/packages.model";

const mockPlans: PackagePlan[] = [
  {
    id: 1,
    name: "Basic",
    currency: "EGP",
    price: 0,
    labelTag: null,
    renewableLabel: "It_will_be_renewed_automatically_after",
    htmlDescription: "<div class=\"package-details\"><div class=\"item\"><span class=\"label\"><span class=\"dot-icon\"></span> الصلاحية </span><span class=\"value\">: 30 يوم</span></div><div class=\"item\"><span class=\"label\"><span class=\"dot-icon\"></span> عدد الإعلانات </span><span class=\"value\">: 12</span></div><div class=\"item\"><span class=\"label\"><span class=\"dot-icon\"></span> النقاط المتاحة </span><span class=\"value\">: 250</span></div><div class=\"item\"><span class=\"label\"><span class=\"dot-icon\"></span> الميزات والعلامة التجارية: </span></div><ul><li><span class=\"dot-icon\"></span> شارة مميزة</li><li><span class=\"dot-icon\"></span> دعم العلامة التجارية</li><li><span class=\"dot-icon\"></span> تحليلات متقدمة</li></ul><div class=\"item\"><span class=\"label\"><span class=\"dot-icon\"></span> الدعم </span><span class=\"value\">: 24/7 عبر واتساب</span></div></div>",
    packageValidDays: 30,
    isCurrentPackage: false,
    packageEndDate: null
  },
  {
    id: 2,
    name: "Pro",
    currency: "EGP",
    price: 299,
    labelTag: "الأكثر شيوعًا",
    renewableLabel: "It_will_be_renewed_automatically_after",
    htmlDescription: "<div class=\"package-details\"><div class=\"item\"><span class=\"label\"><span class=\"dot-icon\"></span> الصلاحية </span><span class=\"value\">: 30 يوم</span></div><div class=\"item\"><span class=\"label\"><span class=\"dot-icon\"></span> عدد الإعلانات </span><span class=\"value\">: 12</span></div><div class=\"item\"><span class=\"label\"><span class=\"dot-icon\"></span> النقاط المتاحة </span><span class=\"value\">: 250</span></div><div class=\"item\"><span class=\"label\"><span class=\"dot-icon\"></span> الميزات والعلامة التجارية: </span></div><ul><li><span class=\"dot-icon\"></span> شارة مميزة</li><li><span class=\"dot-icon\"></span> دعم العلامة التجارية</li><li><span class=\"dot-icon\"></span> تحليلات متقدمة</li></ul><div class=\"item\"><span class=\"label\"><span class=\"dot-icon\"></span> الدعم </span><span class=\"value\">: 24/7 عبر واتساب</span></div></div>",
    packageValidDays: 30,
    isCurrentPackage: true,
    packageEndDate: "28 يناير 2025"
  }
];

const packagesServiceMock = {
  plans: () => mockPlans
};

const meta: Meta<PackageDetailsComponent> = {
  title: "BusinessPackages/Shared/PackageDetails",
  component: PackageDetailsComponent,
  tags: ["autodocs"],
  decorators: [
    moduleMetadata({
      providers: [
        { provide: PackagesService, useValue: packagesServiceMock }
      ]
    })
  ],
  render: (args) => ({ props: args })
};

export default meta;
type Story = StoryObj<PackageDetailsComponent>;

export const CurrentPackage: Story = {
  args: {
    plan: mockPlans[1] 
  }
};

export const NotCurrentPackage: Story = {
  args: {
    plan: mockPlans[0] 
  }
};

export const CustomPlanObject: Story = {
  args: {
    plan: {
      id: 3,
      name: "Enterprise",
      currency: "EGP",
      price: 999,
      labelTag: "مميز",
      renewableLabel: "It_will_be_renewed_automatically_after",
      htmlDescription: "<div class=\"package-details\"><div class=\"item\"><span class=\"label\"><span class=\"dot-icon\"></span> الصلاحية </span><span class=\"value\">: 30 يوم</span></div><div class=\"item\"><span class=\"label\"><span class=\"dot-icon\"></span> عدد الإعلانات </span><span class=\"value\">: 12</span></div><div class=\"item\"><span class=\"label\"><span class=\"dot-icon\"></span> النقاط المتاحة </span><span class=\"value\">: 250</span></div><div class=\"item\"><span class=\"label\"><span class=\"dot-icon\"></span> الميزات والعلامة التجارية: </span></div><ul><li><span class=\"dot-icon\"></span> شارة مميزة</li><li><span class=\"dot-icon\"></span> دعم العلامة التجارية</li><li><span class=\"dot-icon\"></span> تحليلات متقدمة</li></ul><div class=\"item\"><span class=\"label\"><span class=\"dot-icon\"></span> الدعم </span><span class=\"value\">: 24/7 عبر واتساب</span></div></div>",
      packageValidDays: 90,
      isCurrentPackage: false,
      packageEndDate: null
    }
  }
};
