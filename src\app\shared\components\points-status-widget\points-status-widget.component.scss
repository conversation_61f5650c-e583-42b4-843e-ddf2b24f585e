@import "variables";


.ps-card {
    background: rgba(250, 175, 64, 0.1);

    &.over {

        background: rgba(251, 111, 23, 0.1);
    }

    &.clickedMode {
        background: rgba(238, 238, 238, 1);
        cursor: pointer;
    }

    border-radius: $radius-md;
    padding: 12px 14px;
    inline-size: 100%;
    box-sizing: border-box;
    color: $text-muted-2;
    display: grid;
    gap: 8px;
}

.ps-header {
    font-weight: 700;
    color: $gray2;
    display: flex;
    justify-content: space-between;
}

.ps-row {
    display: grid;
    grid-template-columns: 1fr auto;
    align-items: center;
    gap: 10px;
}

.ps-count {
    font-size: $font-size-s;
    color: $gray2;
    font-weight: bold;
    white-space: nowrap;


    &.over {
        color: $red;
        font-weight: 700;
    }
}

.ps-warning-text {
    margin-inline-start: 4px;
}

.ps-bar-wrap {
    position: relative;
    block-size: 6px;
    border-radius: 999px;
    overflow: hidden;
}

.ps-bar-bg {
    position: absolute;
    inset: 0;
    background: lighten($warning-2, 6%);


    &.clickedMode {
        background: #D9D9D9;
    }
}

.ps-bar-fill {
    position: absolute;
    inset-block: 0;
    inset-inline-start: 0;
    background: $orangeColor;
    border-radius: 1rem;

    &.over {
        background: $red;
    }
}

.ps-bar-will-fill {
    position: absolute;
    inset-block: 0;
    inset-inline-start: 0;
    background: lighten($orangeColor, 20%);

    &.over {
        background: $red;
    }
}

.ps-footer {
    font-size: 10px;
    font-weight: 700;
    color: $gray2;
}

.note-icon {
    display: inline-block;
    margin-inline-end: 4px;
}

.ps-details {
    display: grid;
    gap: 4px;
    font-size: 12px;
    font-weight: 400px;
    color: $gray2;

    strong {
        font-weight: bold;

    }
}