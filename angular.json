{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"swapp-frontend": {"projectType": "application", "schematics": {"@schematics/angular:component": {"style": "scss"}}, "root": "", "sourceRoot": "src", "prefix": "app", "architect": {"build": {"builder": "@angular-devkit/build-angular:application", "options": {"statsJson": true, "outputPath": "dist/swapp-frontend", "allowedCommonJsDependencies": ["swappmoment"], "index": "src/index.html", "browser": "src/main.ts", "polyfills": ["zone.js"], "tsConfig": "tsconfig.app.json", "inlineStyleLanguage": "scss", "sourceMap": {"scripts": false, "styles": false, "hidden": true, "vendor": false}, "assets": ["src/favicon.ico", "src/assets", "src/silent-renew.html", "src/robots.txt", "src/sitemap-ar.xml", "src/sitemap-en.xml", "src/manifest.webmanifest", "src/robots.txt", {"glob": "**/*", "input": "src/.well-known", "output": "/.well-known"}], "styles": ["src/styles/theme.scss", "node_modules/primeicons/primeicons.css", "src/styles/main.scss"], "stylePreprocessorOptions": {"includePaths": ["src/styles/style-files"]}, "scripts": [], "server": "src/main.server.ts", "prerender": false, "ssr": {"entry": "server.ts"}, "optimization": {"scripts": true, "styles": {"minify": true, "inlineCritical": true}, "fonts": {"inline": true}}}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "10mb", "maximumError": "10mb"}, {"type": "anyComponentStyle", "maximumWarning": "900kb", "maximumError": "900kb"}], "aot": true, "index": "src/index.prod.html", "optimization": {"scripts": true, "styles": {"minify": true, "inlineCritical": true}, "fonts": true}, "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.prod.ts"}], "outputHashing": "all"}, "staging": {"budgets": [{"type": "initial", "maximumWarning": "10mb", "maximumError": "10mb"}, {"type": "anyComponentStyle", "maximumWarning": "900kb", "maximumError": "900kb"}], "index": "src/index.staging.html", "aot": true, "optimization": {"scripts": true, "styles": {"minify": true, "inlineCritical": true}, "fonts": {"inline": true}}, "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.staging.ts"}], "outputHashing": "all"}, "beta": {"budgets": [{"type": "initial", "maximumWarning": "10mb", "maximumError": "10mb"}, {"type": "anyComponentStyle", "maximumWarning": "900kb", "maximumError": "900kb"}], "optimization": {"scripts": true, "styles": {"minify": true, "inlineCritical": true}, "fonts": true}, "index": "src/index.beta.html", "sourceMap": false, "namedChunks": false, "extractLicenses": true, "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.beta.ts"}], "outputHashing": "all"}, "development": {"optimization": false, "extractLicenses": false, "sourceMap": true, "namedChunks": true, "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.local.ts"}]}}, "defaultConfiguration": "production"}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "configurations": {"production": {"buildTarget": "swapp-frontend:build:production"}, "staging": {"buildTarget": "swapp-frontend:build:staging"}, "development": {"buildTarget": "swapp-frontend:build:development"}}, "defaultConfiguration": "development"}, "serve-ssr": {"builder": "@angular-devkit/build-angular:server", "options": {"main": "src/main.server.ts", "outputPath": "dist/swapp-frontend/server", "tsConfig": "tsconfig.app.json", "inlineStyleLanguage": "scss", "assets": ["src/favicon.ico", "src/assets"], "optimization": true, "sourceMap": false, "namedChunks": false, "extractLicenses": true}}, "prerender": {"builder": "@angular-devkit/build-angular:prerender", "options": {"browserTarget": "swapp-frontend:build:production", "serverTarget": "swapp-frontend:server:production", "routes": ["/home"]}, "configurations": {"production": {}}}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n", "options": {"buildTarget": "swapp-frontend:build"}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"polyfills": ["zone.js", "zone.js/testing"], "tsConfig": "tsconfig.spec.json", "inlineStyleLanguage": "scss", "assets": ["src/favicon.ico", "src/assets", "src/manifest.webmanifest"], "styles": ["src/styles.scss"], "scripts": []}}, "storybook": {"builder": "@storybook/angular:start-storybook", "options": {"configDir": ".storybook", "browserTarget": "swapp-frontend:build", "compodoc": true, "compodocArgs": ["-e", "json", "-d", "."], "port": 6006, "styles": ["src/styles/theme.scss", "node_modules/primeicons/primeicons.css", "src/styles/main.scss"], "stylePreprocessorOptions": {"includePaths": ["src/styles/style-files"]}}}, "build-storybook": {"builder": "@storybook/angular:build-storybook", "options": {"configDir": ".storybook", "browserTarget": "swapp-frontend:build", "compodoc": true, "compodocArgs": ["-e", "json", "-d", "."], "outputDir": "storybook-static", "styles": ["src/styles/theme.scss", "node_modules/primeicons/primeicons.css", "src/styles/main.scss"], "stylePreprocessorOptions": {"includePaths": ["src/styles/style-files"]}}}}}}, "cli": {"analytics": false}}