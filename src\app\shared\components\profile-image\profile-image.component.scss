@import 'variables';

.profile-img {
    position: relative;

    img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        position: relative;
        z-index: 3;
    }

    &::before,
    &::after {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate3d(-50%, -50%, 0);
        background: var(--main-membership-color);
        z-index: 0;
    }

    &.membership {
        &::after {
            background: white;
        }
    }

    /* Business size */
    &.business {
        width: 210px;
        height: 70px;

        img {
            border-radius: 6px;
        }

        &::before,
        &::after {
            width: 215px;
            height: 75px;
            border-radius: 6px;
        }

        &.membership::after {
            width: 212px;
            height: 72px;
        }
    }

    /* Non-business size */
    &.non-business {
        width: 125px;
        height: 125px;

        img {
            border-radius: 50%;
        }

        &::before,
        &::after {
            width: 138px;
            height: 138px;
            border-radius: 50%;
        }

        &.membership::after {
            width: 135px;
            height: 135px;
        }
    }
}