<div class="profile-container" *ngIf="profile">
    <app-profile-cover [isEditMode]="true" [user]="profile"></app-profile-cover>
    <div class="profile-detials">
        <div [ngClass]="profile.businessName ? 'profile-header-business' : 'profile-header-individual'">
            <app-new-profile-header [hideSetting]="true"></app-new-profile-header>
        </div>

        <div class="popular-listings" *ngIf="profile.businessName">
            <div class="section-title">
                <span>استهلاك الباقة</span>

                <button class="upgrade-btn">
                    <span>
                        <app-svg-icons name="crown" width="9px" height="9px"></app-svg-icons>
                    </span>
                    ترقية الباقة
                </button>

            </div>

            <div (click)="goToPoints()">
                <app-points-status-widget title="نقاط" [clickedMode]="true" [usedPoints]="900" [current]="3400" />
            </div>

            <div>
                <app-points-status-widget title="الاعلانات" [clickedMode]="true" [usedPoints]="30" [current]="40" />
            </div>

            <div class="auto-renew">
                <span>
                    <app-svg-icons width="15px" height="15px" name="waring-icon"></app-svg-icons>
                    الباقة سارية حتى 11 فبراير 2025، وتُجدد تلقائيًا. </span>
            </div>
            <app-shared-btn label="اضافات على الحساب" theme="noraml" size="small" iconName="icon-circle-plus"
                iconWidth="10" iconHeight="10"></app-shared-btn>


        </div>

        <div class="popular-listings">
            <div class="section-title">
                <span>الاعلانات الأكثر أداء</span>

                <span class="seeAll">
                    تصفح المزيد <app-svg-icons name="back-icon" width="13px" height="13px"></app-svg-icons>
                </span>

            </div>
            <div>
                <div *ngFor="let listing of ads">
                    <app-listing-card-row [listingDetails]="listing"></app-listing-card-row>
                </div>
            </div>
            <div *ngIf="listing$ | async as listings">
                <ng-container *ngIf="listings.items.length > 0 ;else noListings">
                    <div *ngFor="let listing of listings.items">
                        <app-listing-card-row [listingDetails]="listing"></app-listing-card-row>
                    </div>
                </ng-container>
                <ng-template #noListings>
                    <div class="emptyStateListing">
                        <app-svg-icons name="empty-listings" width="31px" height="31px"></app-svg-icons>
                        <span>
                            لا يوجد اعلانات
                        </span>
                    </div>
                </ng-template>
            </div>
        </div>

        <div class="more-settings">
            <div class="section-title">
                <span>اعدادات اخرى</span>

            </div>
            <div class="section-footer">
                <span class="label">سجل الفوترة والمدفوعات</span>
                <div class="arrow-icon">
                    <app-svg-icons name="icon-arrow-left" width="16px" height="16px" color="#7C7C7C"></app-svg-icons>
                </div>
            </div>

            <div class="section-footer">
                <span class="label">المستندات وتحقق الهوية</span>
                <div class="arrow-icon">
                    <app-svg-icons name="icon-arrow-left" width="16px" height="16px" color="#7C7C7C"></app-svg-icons>
                </div>
            </div>

            <div class="section-footer" *ngIf="!profile.businessName">
                <span class="label">تحويل الي حساب شركة</span>
                <div class="arrow-icon">
                    <app-svg-icons name="icon-arrow-left" width="16px" height="16px" color="#7C7C7C"></app-svg-icons>
                </div>
            </div>
        </div>

    </div>
</div>