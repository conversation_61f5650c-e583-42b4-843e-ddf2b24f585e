<div class="filter-panel p-3">
    <!-- Date Filter -->
    <app-dynamic-header [title]="filtersValues.title" [showBackButton]="false" />
    <div>
        <app-calendar [(selectedDateRange)]="filtersValues.selectedDateRange" title="تاريخ الانشاء"></app-calendar>
    </div>
    <!-- Sort Options -->
    <h3 class="section-title">ترتيب الظهور</h3>
    <div class="quick-buttons">
        <button type="button" (click)="filtersValues.sortOption = 'newest'"
            [class.active]="filtersValues.sortOption === 'newest'">
            الاعلانات الجديدة أولاً
        </button>
        <button type="button" (click)="filtersValues.sortOption = 'oldest'"
            [class.active]="filtersValues.sortOption === 'oldest'">
            الاعلانات الأقدم أولاً"
        </button>
    </div>

    <!-- Call Range -->
    <h3 class="section-title">تخصيص البحث</h3>
    <p class="filter-label">المكالمات</p>
    <div class="quick-buttons">
        <button *ngFor="let option of ['أقل من 10', '10 - 30', '30 - 50', 'أكثر من 50']" type="button"
            (click)="filtersValues.callRange = option" [class.active]="filtersValues.callRange === option">
            {{option}}
        </button>
    </div>

    <!-- Message Range -->
    <p class="filter-label">الرسائل</p>
    <div class="quick-buttons">
        <button *ngFor="let option of ['أقل من 10', '10 - 30', '30 - 50', '50 - 70', '70 - 100', 'أكثر من 100']"
            type="button" (click)="filtersValues.messageRange = option"
            [class.active]="filtersValues.messageRange === option">
            {{option}}
        </button>
    </div>

    <app-shared-btn label="تطبيق" (btnClick)="close()"></app-shared-btn>

</div>