import { Meta, StoryObj, moduleMetadata } from "@storybook/angular";
import { CommonModule } from "@angular/common";
import { BrowserAnimationsModule } from "@angular/platform-browser/animations";
import { TranslateModule } from "@ngx-translate/core";
import { PointsComponent } from "./points.component";
import { DynamicHeaderComponent } from "@src/app/shared/components/dynamic-header/dynamic-header.component";
import { PointsStatusWidgetComponent } from "@src/app/shared/components/points-status-widget/points-status-widget.component";
import { AlertHandlerService } from "@src/app/modules/core/alerts/alert-handler.service";

type Story = StoryObj<PointsComponent>;

const meta: Meta<PointsComponent> = {
  title: "Profile/PointsPage",
  component: PointsComponent,
  tags: ["autodocs"],
  decorators: [
    moduleMetadata({
      imports: [
        CommonModule,
        BrowserAnimationsModule,
        TranslateModule.forRoot(),
        DynamicHeaderComponent,
        PointsStatusWidgetComponent,
      ],
      providers: [
        {
          provide: AlertHandlerService,
          useValue: {
            DynamicDialogOpen: () => {},
            showError: () => {},
            showSuccess: () => {},
            confirm: () => Promise.resolve(true),
          },
        },
      ],
    }),
  ],
  argTypes: { pointsData: { control: "object" } },
  render: (args) => ({ props: args }),
};

export default meta;

const sampleData = [
  {
    price: "2,340",
    currency: "EGP",
    label: "نقاط الباقة الاساسية",
    expiresAt: "11 فبراير 2025",
    pointsText: "+2,340 نقطة",
    buyBtnText: "شراء",
    dateLabel: "تاريخ الانتهاء :",
  },
  {
    price: "2,340",
    currency: "EGP",
    label: "نقاط الباقة الاساسية",
    expiresAt: "11 فبراير 2025",
    pointsText: "+2,340 نقطة",
    buyBtnText: "شراء",
    dateLabel: "تاريخ الانتهاء :",
  }
];

export const WithData: Story = {
  args: {
    pointsData: sampleData,
    price: "2,340",
    currency: "EGP",
    label: "نقاط الباقة الاساسية",
    expiresAt: "11 فبراير 2025",
    pointsText: "+2,340 نقطة",
    buyBtnText: "شراء",
    dateLabel: "تاريخ الانتهاء :",
    buyBtnBg: "#ECFDF3",
    buyBtnTextColor: "#027A48",
},
};
export const WithDataRedLabel: Story = {
  args: {
    pointsData: sampleData,
    price: "2,340",
    currency: "EGP",
    label: "نقاط الباقة الاساسية",
    expiresAt: "11 فبراير 2025",
    pointsText: "+2,340 نقطة",
    buyBtnText: "استخدام",
    dateLabel: "تاريخ الانتهاء :",
    buyBtnBg: "#FEF3F2",
    buyBtnTextColor: "#B42318",
},
};
export const Empty: Story = {
  args: { pointsData: [] } };
