import { NgModule } from "@angular/core";
import { RouterModule, Routes } from "@angular/router";
import { RatingsComponent } from "./pages/ratings/ratings.component";
import { ManageAccountComponent } from "./pages/manage-account/manage-account.component";
import { NewProfileLayoutComponent } from "./layouts/new-profile-layout/new-profile-layout.component";
import { PofileIndexComponent } from "./pages/pofile-index/pofile-index.component";
import { PointsComponent } from "./components/points/points.component";
import { EditProfileComponent } from "./pages/edit-profile/edit-profile.component";

const routes: Routes = [
  {
    path: '',
    redirectTo: 'index',
    pathMatch: 'full'
  },
  {
    path: "",
    component: NewProfileLayoutComponent,
    children: [
      {
        path: "index",
        component: PofileIndexComponent
      },
      {
        path: "manage-account",
        component: ManageAccountComponent
      },
      {
        path: "edit-profile",
        component: EditProfileComponent
      },
      {
        path: "ratings",
        component: RatingsComponent
      },
      {
        path: "points",
        component: PointsComponent
      },
    ],
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class NewProfileRoutingModule { }