@import 'variables';
@import "mixins";

.profile_cover_box {
    height: 190px;
    overflow: hidden;
    position: relative;
    background: linear-gradient(181.64deg, rgba(123, 69, 172, 0.1) 1.39%, var(--opacity-secondary-color) 100.4%);



    img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        object-position: top right;
        position: relative;
        z-index: 2;
    }


    @include Large {
        height: 284px;

        border-radius: 12px;

        &::after {
            background-size: contain;
        }

    }

}

app-dynamic-header {
    width: 100%;
    margin: 0rem 1.5rem;
}

.filterHeader {
    color: rgba(114, 34, 130, 1);
    font-size: 12px;
    font-weight: 800px;
}

.profile-cover-btn {
    position: absolute;
    top: 16px;
    display: flex;
    gap: 15px;
    justify-content: center;
    align-items: center;
    z-index: 3;
    font-weight: bold;
    font-size: 0.85rem;
    width: 100%;


    input {
        display: none;
    }
}

.camera_icon {
    width: 35px;
    height: 35px;
    background: $text-color;
    border-radius: 50%;
    padding: 9px;
    display: flex;
    justify-content: center;
    align-items: center;

    svg {
        width: 100%;
        height: 100%;
        color: #fff;
    }
}