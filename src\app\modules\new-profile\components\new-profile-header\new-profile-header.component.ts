import { Router } from '@angular/router';
import { CommonModule } from '@angular/common';
import { Component, Input } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { ProfileImageComponent } from '@src/app/shared/components/profile-image/profile-image.component';
import { StarsWidgetComponent } from '@src/app/shared/components/stars-widget/stars-widget.component';
import { SvgIconsComponent } from '@src/app/shared/components/svg-icons/svg-icons.component';
import { ProfileModel } from '@src/app/shared/models/profile.model';
import { ProfileService } from '@src/app/shared/services/profile.service';
import { RatingModule } from 'primeng/rating';
import { Observable } from 'rxjs';
import { BusinessPackageCycleComponent } from '@src/app/modules/business-package/pages/business-package-cycle/business-package-cycle.component';
import { AlertHandlerService } from '@src/app/modules/core/alerts/alert-handler.service';

@Component({
  selector: 'app-new-profile-header',
  standalone: true,
  imports: [SvgIconsComponent, RatingModule, FormsModule, CommonModule, StarsWidgetComponent, ProfileImageComponent],
  templateUrl: './new-profile-header.component.html',
  styleUrl: './new-profile-header.component.scss'
})
export class NewProfileHeaderComponent {
  profile$: Observable<ProfileModel>;


  @Input() hideSetting = false
  @Input() btnTitle = ''

  constructor(private profileService: ProfileService,
    private alertHandlerService: AlertHandlerService,
    private router: Router,
  ) { }

  ngOnInit(): void {

    this.profile$ = this.profileService.profile;
  }

  goTo(path: 'manage-account' | 'ratings') {
    this.router.navigate([`/authentication/new-profile/${path}`]);
  }


  openPackages() {

    this.alertHandlerService.DynamicDialogOpen(
      BusinessPackageCycleComponent,
      {
        className: 'basicModal'
      },
      (callbackData: any) => {
        if (callbackData) {
        }
      }
    );
  }
}