import { CommonModule } from '@angular/common';
import { Component, Input } from '@angular/core';
import { SvgIconsComponent } from '@src/app/shared/components/svg-icons/svg-icons.component';

@Component({
  selector: 'app-custom-details',
  standalone: true,
  imports: [CommonModule, SvgIconsComponent],
  templateUrl: './custom-details.component.html',
  styleUrl: './custom-details.component.scss'
})
export class CustomDetailsComponent {
  @Input() price?: string = '';
  @Input() duration!: string;
  @Input() adCount!: string;
  @Input() availablePoints!: string;
  @Input() features: string[] = [];
  @Input() support!: string;
  @Input() renewalNote: string = '';
}

