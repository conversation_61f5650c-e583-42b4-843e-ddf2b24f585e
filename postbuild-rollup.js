const glob = require("glob");
const { exec } = require("child_process");

// Search for the main bundle file in the browser output directory
glob.glob("dist/swapp-frontend/browser/main*.js", (err, files) => {
  if (err || files.length === 0) {
    console.error("Main bundle file not found!");
    return;
  }
  const mainFile = files[0];
  const rollupCmd = `rollup --format es -d dist/swapp-frontend/browser/ --experimentalMinChunkSize 1000 -- ${mainFile}`;

  // Execute the Rollup command
  exec(rollupCmd, (error, stdout, stderr) => {
    if (error) {
      console.error(`Rollup error: ${error.message}`);
      return;
    }
    if (stderr) {
      console.error(`Rollup stderr: ${stderr}`);
      return;
    }
    console.log(`Rollup output:\n${stdout}`);
  });
});

glob.glob("dist/swapp-frontend/server/server.mjs", (err, files) => {
  if (err || files.length === 0) {
    console.error("Main bundle file not found!");
    return;
  }
  const mainFile = files[0];
  const rollupCmd = `rollup --format es -d dist/swapp-frontend/server/ --experimentalMinChunkSize 1000 -- ${mainFile}`;

  // Execute the Rollup command
  exec(rollupCmd, (error, stdout, stderr) => {
    if (error) {
      console.error(`Rollup error: ${error.message}`);
      return;
    }
    if (stderr) {
      console.error(`Rollup stderr: ${stderr}`);
      return;
    }
    console.log(`Rollup output:\n${stdout}`);
  });
});
