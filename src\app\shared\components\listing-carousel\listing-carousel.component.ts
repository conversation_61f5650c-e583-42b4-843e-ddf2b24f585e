import { CommonModule, isPlatformBrowser } from '@angular/common';
import { AfterViewInit, Component, ElementRef, Inject, Input, OnChanges, OnDestroy, OnInit, PLATFORM_ID, ViewChild, ViewEncapsulation, computed, effect, signal } from '@angular/core';
import { AdsSectionDTO } from '../../models/lookup.model';
import { AnimatedSmoothIndicatorComponent, ScrollingDotsEffect } from '../animated-smooth-indicator/animated-smooth-indicator.component';
import { ListingSlideImageComponent } from '../listing-slide-image/listing-slide-image.component';
import { MpuContainerComponent } from '../mpu-container/mpu-container.component';

/**
 * CAROUSEL ITEM INTERFACE
 * Defines the structure of items that can be displayed in the carousel
 */
export interface CarouselItem {
    type: 'image' | 'ads';
    ads?: AdsSectionDTO;           // Advertisement data (only for ads type)
    data?: {
        imageUrl?: string;         // Image URL (for image type)
        link?: string;            // Click destination URL
        [key: string]: any;       // Additional flexible data
    };
}

/**
 * LISTING CAROUSEL COMPONENT
 * 
 * A responsive, touch-enabled carousel component that supports:
 * - Image and advertisement slides
 * - RTL/LTR layouts
 * - Autoplay functionality with smart pause on user interaction
 * - Touch/mouse drag navigation
 * - Navigation arrows and indicators
 * - Dynamic slide removal (for ads with view limits)
 * 
 * ARCHITECTURE:
 * - Uses Angular Signals for reactive state management
 * - Computed properties for derived state
 * - Effects for side effects (autoplay, RTL updates)
 * - Browser-safe with SSR support
 */
@Component({
    selector: 'app-listing-carousel',
    standalone: true,
    imports: [CommonModule, ListingSlideImageComponent, MpuContainerComponent, AnimatedSmoothIndicatorComponent],
    templateUrl: './listing-carousel.component.html',
    styleUrls: ['./listing-carousel.component.scss'],
    encapsulation: ViewEncapsulation.None
})
export class ListingCarouselComponent implements OnInit, OnDestroy, OnChanges, AfterViewInit {
    
    // ========================================
    // REACTIVE STATE (SIGNALS)
    // ========================================
    
    /** Core carousel data and configuration */
    items = signal<CarouselItem[]>([]);
    showArrows = signal<boolean>(true);
    showIndicators = signal<boolean>(true);
    autoplay = signal<boolean>(false);
    autoplayInterval = signal<number>(5000);
    isRtl = signal<boolean>(false);
    
    /** Navigation and interaction state */
    currentIndex = signal<number>(0);
    isDragging = signal<boolean>(false);
    translateX = signal<number>(0);
    bigMode = signal(false);
    
    /** Internal state */
    isInitialized = signal<boolean>(false);
    isBrowser = signal<boolean>(false);
    private isAutoplayPaused = signal<boolean>(false);
    
    // ========================================
    // COMPUTED PROPERTIES (DERIVED STATE)
    // ========================================
    
    /** Whether carousel has multiple items (enables navigation) */
    hasMultipleItems = computed(() => this.items().length > 1);
    
    /** Whether to show navigation arrows */
    shouldShowArrows = computed(() => this.showArrows() && this.hasMultipleItems());
    
    /** Whether to show slide indicators */
    shouldShowIndicators = computed(() => this.showIndicators() && this.hasMultipleItems());
    
    /** Whether autoplay should be active */
    shouldAutoplay = computed(() => this.autoplay() && this.hasMultipleItems() && !this.isAutoplayPaused());
    
    // ========================================
    // VIEW REFERENCES & CONFIGURATION
    // ========================================
    
    @ViewChild('carouselContainer', { static: false }) carouselContainer!: ElementRef<HTMLDivElement>;
    @ViewChild('carouselTrack', { static: false }) carouselTrack!: ElementRef<HTMLDivElement>;
    
    /** Indicator styling configuration */
    smoothIndicatorEffect: ScrollingDotsEffect = {
        dotWidth: 8,
        dotHeight: 8,
        spacing: 6,
        maxVisibleDots: 7,
        activeDotColor: '#007aff',
        dotColor: '#ffffffa2',
        animationDuration: 300
    };
    
    // ========================================
    // PRIVATE PROPERTIES
    // ========================================
    
    private autoplayTimer: any;
    private autoplayPauseTimer: any;
    private dragStartPos: number = 0;
    private dragCurrentPos: number = 0;
    private dragThreshold: number = 50;
    private readonly AUTOPLAY_PAUSE_DURATION = 3000; // 3 seconds
    
    // ========================================
    // INPUT SETTERS (EXTERNAL API)
    // ========================================
    
    @Input() set itemsInput(value: CarouselItem[]) { this.items.set(value); }
    @Input() set showArrowsInput(value: boolean) { this.showArrows.set(value); }
    @Input() set showIndicatorsInput(value: boolean) { this.showIndicators.set(value); }
    @Input() set autoplayInput(value: boolean) { this.autoplay.set(value); }
    @Input() set autoplayIntervalInput(value: number) { this.autoplayInterval.set(value); }
    @Input() set isRtlInput(value: boolean) { this.isRtl.set(value); }
    
    // ========================================
    // CONSTRUCTOR & LIFECYCLE HOOKS
    // ========================================
    
    constructor(@Inject(PLATFORM_ID) private platformId: Object) {
        this.isBrowser.set(isPlatformBrowser(this.platformId));
        this.setupEffects();
    }
    
    ngOnInit(): void {
        // Initialization logic happens in ngAfterViewInit
    }
    
    ngAfterViewInit(): void {
        // Initialize carousel after view is ready
        setTimeout(() => {
            this.isInitialized.set(true);
            this.updateCarouselPosition();
        });
    }
    
    ngOnChanges(): void {
        // Reset carousel when items change
        this.resetCarouselState();
    }
    
    ngOnDestroy(): void {
        this.cleanupTimers();
    }
    
    // ========================================
    // SETUP & INITIALIZATION
    // ========================================
    
    /**
     * Sets up reactive effects for autoplay and RTL changes
     */
    private setupEffects(): void {
        // Effect: Handle RTL direction changes
        effect(() => {
            if (this.isInitialized()) {
                this.updateCarouselPosition();
            }
        }, { allowSignalWrites: true });
        
        // Effect: Handle autoplay state changes
        effect(() => {
            if (this.shouldAutoplay()) {
                this.startAutoplay();
            } else {
                this.stopAutoplay();
            }
        }, { allowSignalWrites: true });
    }
    
    /**
     * Resets carousel to initial state
     */
    private resetCarouselState(): void {
        if (this.isInitialized()) {
            this.currentIndex.set(0);
            this.translateX.set(0);
            this.updateCarouselPosition();
        }
    }
    
    /**
     * Cleans up all timers on component destruction
     */
    private cleanupTimers(): void {
        this.stopAutoplay();
        if (this.autoplayPauseTimer) {
            clearTimeout(this.autoplayPauseTimer);
        }
    }
    
    // ========================================
    // NAVIGATION METHODS
    // ========================================
    
    /**
     * Navigate to a specific slide by index
     * @param index Target slide index
     */
    goToSlide(index: number): void {
        const items = this.items();
        const currentIdx = this.currentIndex();
        
        if (this.isValidSlideIndex(index) && index !== currentIdx) {
            this.currentIndex.set(index);
            this.updateCarouselPosition();
            this.pauseAutoplayForManualNavigation();
        }
    }
    
    /**
     * Move to the next slide (with wrapping)
     */
    nextSlide(): void {
        if (!this.hasMultipleItems()) return;
        
        const currentIdx = this.currentIndex();
        const nextIndex = (currentIdx + 1) % this.items().length;
        this.goToSlide(nextIndex);
    }
    
    /**
     * Move to the previous slide (with wrapping)
     */
    prevSlide(): void {
        if (!this.hasMultipleItems()) return;
        
        const currentIdx = this.currentIndex();
        const itemsLength = this.items().length;
        const prevIndex = (currentIdx - 1 + itemsLength) % itemsLength;
        this.goToSlide(prevIndex);
    }
    
    /**
     * Validates if slide index is within bounds
     */
    private isValidSlideIndex(index: number): boolean {
        return index >= 0 && index < this.items().length;
    }
    
    /**
     * Updates the visual position of the carousel track
     */
    private updateCarouselPosition(): void {
        if (!this.isInitialized() || !this.carouselTrack) return;
        
        const currentIdx = this.currentIndex();
        const isRtl = this.isRtl();
        
        // Calculate translation: RTL reverses direction
        const translateValue = isRtl ? currentIdx * 100 : -currentIdx * 100;
        this.translateX.set(translateValue);
        
        // Apply smooth transition
        const track = this.carouselTrack.nativeElement;
        track.style.transition = 'transform 0.3s ease-in-out';
        track.style.transform = `translateX(${translateValue}%)`;
    }
    
    // ========================================
    // AUTOPLAY MANAGEMENT
    // ========================================
    
    /**
     * Starts the autoplay timer
     */
    private startAutoplay(): void {
        if (this.isBrowser() && this.shouldAutoplay() && !this.isAutoplayPaused()) {
            this.stopAutoplay(); // Ensure no duplicate timers
            this.autoplayTimer = setInterval(() => {
                if (!this.isAutoplayPaused()) {
                    this.nextSlide();
                }
            }, this.autoplayInterval());
        }
    }

    private stopAutoplay(): void {
        if (this.autoplayTimer) {
            clearInterval(this.autoplayTimer);
            this.autoplayTimer = null;
        }
    }

    private pauseAutoplayForManualNavigation(): void {
        this.isAutoplayPaused.set(true);
        this.stopAutoplay();
        
        // Clear any existing pause timer
        if (this.autoplayPauseTimer) {
            clearTimeout(this.autoplayPauseTimer);
        }
        
        // Resume autoplay after 3 seconds of no manual interaction
        this.autoplayPauseTimer = setTimeout(() => {
            this.isAutoplayPaused.set(false);
            this.startAutoplay();
        }, 3000);
    }

    onTouchStart(event: TouchEvent): void {
        const items = this.items();
        if (!this.isBrowser() || items.length <= 1) return;

        this.isDragging.set(true);
        this.dragStartPos = event.touches[0].clientX;
        this.dragCurrentPos = this.dragStartPos;
        this.stopAutoplay();

        if (this.carouselTrack) {
            this.carouselTrack.nativeElement.style.transition = 'none';
        }
    }

    onTouchMove(event: TouchEvent): void {
        const items = this.items();
        if (!this.isBrowser() || !this.isDragging() || items.length <= 1) return;

        event.preventDefault();
        const newCurrentX = event.touches[0].clientX;
        this.dragCurrentPos = newCurrentX;
        const diff = this.dragStartPos - newCurrentX;
        const currentIdx = this.currentIndex();
        const isRtl = this.isRtl();

        // Adjust for RTL
        const currentTranslate = isRtl ? currentIdx * 100 : -currentIdx * 100;
        const dragTranslate = (diff / this.carouselContainer.nativeElement.offsetWidth) * 100;
        const finalTranslate = isRtl ? currentTranslate + dragTranslate : currentTranslate - dragTranslate;

        if (this.carouselTrack) {
            this.carouselTrack.nativeElement.style.transform = `translateX(${finalTranslate}%)`;
        }
    }

    onTouchEnd(event: TouchEvent): void {
        const items = this.items();
        if (!this.isBrowser() || !this.isDragging() || items.length <= 1) return;

        this.isDragging.set(false);
        const diff = this.dragStartPos - this.dragCurrentPos;
        const isRtl = this.isRtl();

        if (this.carouselTrack) {
            this.carouselTrack.nativeElement.style.transition = 'transform 0.3s ease-in-out';
        }

        if (Math.abs(diff) > this.dragThreshold) {
            if (isRtl) {
                if (diff > 0) {
                    this.prevSlide();
                } else {
                    this.nextSlide();
                }
            } else {
                if (diff > 0) {
                    this.nextSlide();
                } else {
                    this.prevSlide();
                }
            }
        } else {
            this.updateCarouselPosition();
        }

        this.resetAutoplay();
    }

    onMouseDown(event: MouseEvent): void {
        const items = this.items();
        if (!this.isBrowser() || items.length <= 1) return;

        this.isDragging.set(true);
        this.dragStartPos = event.clientX;
        this.dragCurrentPos = this.dragStartPos;
        this.stopAutoplay();
        event.preventDefault();

        if (this.carouselTrack) {
            this.carouselTrack.nativeElement.style.transition = 'none';
        }
    }

    onMouseMove(event: MouseEvent): void {
        const items = this.items();
        if (!this.isBrowser() || !this.isDragging() || items.length <= 1) return;

        const newCurrentX = event.clientX;
        this.dragCurrentPos = newCurrentX;
        const diff = this.dragStartPos - newCurrentX;
        const currentIdx = this.currentIndex();
        const isRtl = this.isRtl();

        const currentTranslate = isRtl ? currentIdx * 100 : -currentIdx * 100;
        const dragTranslate = (diff / this.carouselContainer.nativeElement.offsetWidth) * 100;
        const finalTranslate = isRtl ? currentTranslate + dragTranslate : currentTranslate - dragTranslate;

        if (this.carouselTrack) {
            this.carouselTrack.nativeElement.style.transform = `translateX(${finalTranslate}%)`;
        }
    }

    onMouseUp(event: MouseEvent): void {
        const items = this.items();
        if (!this.isBrowser() || !this.isDragging() || items.length <= 1) return;

        this.isDragging.set(false);
        const diff = this.dragStartPos - this.dragCurrentPos;
        const isRtl = this.isRtl();

        // Restore transition
        if (this.carouselTrack) {
            this.carouselTrack.nativeElement.style.transition = 'transform 0.3s ease-in-out';
        }

        if (Math.abs(diff) > this.dragThreshold) {
            if (isRtl) {
                if (diff > 0) {
                    this.prevSlide();
                } else {
                    this.nextSlide();
                }
            } else {
                if (diff > 0) {
                    this.nextSlide();
                } else {
                    this.prevSlide();
                }
            }
        } else {
            this.updateCarouselPosition();
        }

        this.resetAutoplay();
    }

    onMouseLeave(): void {
        if (this.isDragging()) {
            this.isDragging.set(false);
            // Restore transition
            if (this.carouselTrack) {
                this.carouselTrack.nativeElement.style.transition = 'transform 0.3s ease-in-out';
            }
            this.updateCarouselPosition();
            this.resetAutoplay();
        }
    }

    // Item click handler
    onItemClick(item: CarouselItem, event: Event): void {
        if (Math.abs(this.dragStartPos - this.dragCurrentPos) > 10) {
            event.preventDefault();
            return;
        }

        if (item.type === 'ads' && item.data?.link) {
            if (this.isBrowser()) {
                window.open(item.data.link, '_blank');
            }
        }
    }

    // ========================================
    // UTILITY METHODS & TYPE GUARDS
    // ========================================

    /**
     * Type guard for image carousel items
     */
    isImageType(item: CarouselItem): boolean {
        return item.type === 'image';
    }

    /**
     * Type guard for ads carousel items
     */
    isAdsType(item: CarouselItem): boolean {
        return item.type === 'ads';
    }

    /**
     * Gets image URL from carousel item
     */
    getImageUrl(item: CarouselItem): string {
        return item.data?.imageUrl || '';
    }

    /**
     * Gets ads link from carousel item
     */
    getAdsLink(item: CarouselItem): string {
        return item.data?.link || '';
    }

    // ========================================
    // ADS MANAGEMENT
    // ========================================

    /**
     * Handles hiding ads when view limits are exceeded
     * Called by MPU container component when ads should be removed
     */
    onShouldHideAds(index: number, shouldHide: boolean): void {
        if (!shouldHide) return;

        const currentItems = this.items();
        const updatedItems = currentItems.filter((_, i) => i !== index);
        this.items.set(updatedItems);

        // Adjust current index to prevent out-of-bounds
        this.adjustCurrentIndexAfterRemoval(index, updatedItems.length);

        // Update visual position
        if (this.isInitialized()) {
            this.updateCarouselPosition();
        }
    }

    /**
     * Adjusts current index after item removal
     */
    private adjustCurrentIndexAfterRemoval(removedIndex: number, newLength: number): void {
        const currentIdx = this.currentIndex();

        if (currentIdx >= removedIndex && currentIdx > 0) {
            this.currentIndex.set(currentIdx - 1);
        } else if (currentIdx >= newLength && newLength > 0) {
            this.currentIndex.set(newLength - 1);
        }
    }

    /**
     * Legacy method for backward compatibility
     * @deprecated Use pauseAutoplayForManualNavigation instead
     */
    private resetAutoplay(): void {
        if (this.shouldAutoplay()) {
            this.pauseAutoplayForManualNavigation();
        }
    }


}
