import { Component, inject, Input, ViewEncapsulation } from "@angular/core";
import { SharedBtnComponent } from "@src/app/shared/components/shared-btn/shared-btn.component";
import { PackageDetailsComponent } from "../package-details/package-details.component";
import { CommonModule } from "@angular/common";
import { ViewSwitchService } from "../../services/view-switch.service";
import { PackagesService } from "../../services/packages.service";
import { PackagePlan } from "../../models/packages.model";

@Component({
  selector: "app-package-plan",
  standalone: true,
  imports: [
    SharedBtnComponent,
    PackageDetailsComponent,
    CommonModule,
  ],
  templateUrl: "./package-plan.component.html",
  styleUrl: "./package-plan.component.scss",
  encapsulation: ViewEncapsulation.None,
})
export class PackagePlanComponent {
  @Input() plan!: PackagePlan | null;
  @Input() isSubscribed: boolean = false;

  constructor(
    private view: ViewSwitchService,
    public packagesService: PackagesService
  ) { }

  selectPlan() {
    this.packagesService.selectPlan(this.plan!.id);
    this.view.go("subscription");
  }
  unsubscrip() {
    this.packagesService.selectPlan(this.plan!.id);
    this.view.go("unsubscribe");
  }

}
