import { Meta, StoryObj, moduleMetadata } from "@storybook/angular";
import { SubscriptionPackageComponent } from "./subscription-package.component";
import { PackagesService } from "../../services/packages.service";
import { ViewSwitchService } from "../../services/view-switch.service";
import { PackagePlan } from "../../models/packages.model";
import { DynamicHeaderComponent } from "@src/app/shared/components/dynamic-header/dynamic-header.component";
import { AlertHandlerService } from "@src/app/modules/core/alerts/alert-handler.service";


const basicPlan: PackagePlan = {
  id: 1,
  name: "Basic",
  currency: "EGP",
  price: 3262,
  labelTag: null,
  renewableLabel: "It_will_be_renewed_automatically_after",
  htmlDescription:
    '<div class="package-details"><div class="item"><span class="label"><span class="dot-icon"></span> الصلاحية </span><span class="value">: 30 يوم</span></div><div class="item"><span class="label"><span class="dot-icon"></span> عدد الإعلانات </span><span class="value">: 12</span></div><div class="item"><span class="label"><span class="dot-icon"></span> النقاط المتاحة </span><span class="value">: 250</span></div><div class="item"><span class="label"><span class="dot-icon"></span> الميزات والعلامة التجارية: </span></div><ul><li><span class="dot-icon"></span> شارة مميزة</li><li><span class="dot-icon"></span> دعم العلامة التجارية</li><li><span class="dot-icon"></span> تحليلات متقدمة</li></ul><div class="item"><span class="label"><span class="dot-icon"></span> الدعم </span><span class="value">: 24/7 عبر واتساب</span></div></div>',
  packageValidDays: 30,
  isCurrentPackage: false,
  packageEndDate: null,
};

const proPlan: PackagePlan = {
  id: 2,
  name: "Pro",
  currency: "EGP",
  price: 299,
  labelTag: "الأكثر شيوعًا",
  renewableLabel: "It_will_be_renewed_automatically_after",
  htmlDescription:
    '<div class="package-details"><div class="item"><span class="label"><span class="dot-icon"></span> الصلاحية </span><span class="value">: 30 يوم</span></div><div class="item"><span class="label"><span class="dot-icon"></span> عدد الإعلانات </span><span class="value">: 12</span></div><div class="item"><span class="label"><span class="dot-icon"></span> النقاط المتاحة </span><span class="value">: 250</span></div><div class="item"><span class="label"><span class="dot-icon"></span> الميزات والعلامة التجارية: </span></div><ul><li><span class="dot-icon"></span> شارة مميزة</li><li><span class="dot-icon"></span> دعم العلامة التجارية</li><li><span class="dot-icon"></span> تحليلات متقدمة</li></ul><div class="item"><span class="label"><span class="dot-icon"></span> الدعم </span><span class="value">: 24/7 عبر واتساب</span></div></div>',
  packageValidDays: 30,
  isCurrentPackage: false,
  packageEndDate: null,
};


const makePackagesServiceMock = (plan: PackagePlan) => ({
  selectedPlan: () => plan,
  selectPlan: (_: number) => {},
});


const meta: Meta<SubscriptionPackageComponent> = {
  title: "BusinessPackages/SubscriptionPackage",
  component: SubscriptionPackageComponent,
  tags: ["autodocs"],
  decorators: [
    moduleMetadata({
      providers: [
        { provide: PackagesService, useValue: makePackagesServiceMock(basicPlan) },
        { provide: ViewSwitchService, useValue: {} },
        { provide: AlertHandlerService , useValue: {} } ,
        { provide: DynamicHeaderComponent, useValue: {} },
      ],
    }),
  ],
  render: (args) => ({ props: args }),
};

export default meta;
type Story = StoryObj<SubscriptionPackageComponent>;


export const Default: Story = {};

export const BusinessPro: Story = {
  decorators: [
    moduleMetadata({
      providers: [
        { provide: PackagesService, useValue: makePackagesServiceMock(proPlan) },
      ],
    }),
  ],
};
