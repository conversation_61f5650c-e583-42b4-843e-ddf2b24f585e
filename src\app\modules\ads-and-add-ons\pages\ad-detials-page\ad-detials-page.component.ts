import { CommonModule } from '@angular/common';
import { Component } from '@angular/core';
import { DynamicHeaderComponent } from '@src/app/shared/components/dynamic-header/dynamic-header.component';
import { NtranslatePipe } from '@src/app/shared/pipes/ntranslate.pipe';
import { StatsBlockComponent } from '../../components/stats-block/stats-block.component';
import { AlertHandlerService } from '@src/app/modules/core/alerts/alert-handler.service';
import { FilterPanelComponent } from '../../components/filter-panel/filter-panel.component';
import { ChartLineWidgetComponent } from "@src/app/shared/components/chart-line-widget/chart-line-widget.component";
import { SvgIconsComponent } from '@src/app/shared/components/svg-icons/svg-icons.component';
import { SharedBtnComponent } from '@src/app/shared/components/shared-btn/shared-btn.component';
import { AdsAndAddOnsSheetCycleComponent } from '../../ads-and-add-ons-sheet-cycle/ads-and-add-ons-sheet-cycle.component';
import { ProfileService } from '@src/app/shared/services/profile.service';

@Component({
  selector: 'app-ad-detials-page',
  standalone: true,
  imports: [StatsBlockComponent, CommonModule, NtranslatePipe, DynamicHeaderComponent, ChartLineWidgetComponent, SvgIconsComponent, SharedBtnComponent],
  templateUrl: './ad-detials-page.component.html',
  styleUrl: './ad-detials-page.component.scss'
})
export class AdDetialsPageComponent {

  dashboardStats = [
    { label: "إجمالي المكالمات", value: 231, icon: 'phone-icon' },
    { label: "إجمالي الرسائل", value: 231, icon: 'message-icon' },
    { label: "إجمالي العروض", value: 231, icon: 'eye-icon' },
    { label: "إجمالي المشاهدات", value: 231, icon: 'arrow-top-left' },
    { label: "إجمالي النقرات", value: 231, icon: 'tag-icon' },
  ];

  chartData = [
    { name: 'إجمالي المكالمات', data: [40, 30, 40, 50, 40, 30, 50] },
    { name: 'إجمالي الرسائل', data: [0, 10, 20, 30, 40, 50, 60] },
    { name: 'إجمالي المشاهدات', data: [60, 50, 40, 30, 20, 10, 0] },
    { name: 'إجمالي النقرات', data: [20, 40, 25, 15, 10, 40, 50] },
    { name: 'إجمالي العروض', data: [20, 40, 25, 15, 10, 40, 50] },
  ]

  ads = {
    id: 1,
    title: 'شقة للبيع متشطبة استلام فوري',
    webpImageURL: '../../../../../../assets/images/image 4.jpg',
    callCount: 200,
    viewCount: 32,
    appearanceCount: 103,
    tags: ['Top Featured'],
  }

  filtersValues = {
    sortOption: '',
    callRange: '',
    messageRange: '',
    title: 'اختار تاريخ الاحصائيات',
    selectedDateRange: [],
  }

  selectedAds = new Set<number>();

  constructor(private alertHandlerService: AlertHandlerService, private profileService: ProfileService) { }

  isBusiness = false;

  ngOnInit(): void {
    this.profileService.profile.subscribe((res) => {
      this.isBusiness = res.businessName !== null
    })
  }

  openFilter() {
    this.alertHandlerService.DynamicDialogOpen<FilterPanelComponent>(FilterPanelComponent, this.filtersValues, (callbackData: any) => {
      if (callbackData) {
        this.filtersValues = callbackData;
      }
    })
  }

  openAddon() {
    this.alertHandlerService.DynamicDialogOpen<AdsAndAddOnsSheetCycleComponent>(AdsAndAddOnsSheetCycleComponent, this.filtersValues, (callbackData: any) => {
      if (callbackData) {
        this.filtersValues = callbackData;
      }
    })
  }
}
