$primary: #722282;
$primary-dark: #683b90;
$secondary: #f7f7fb;
$warning: #f17c2d;
$green: rgb(16, 161, 16);
$grey-1: #fbfafe;
$grey-2: #f7f7fb;
$text-color: #2d3142;
$side-nav-link-color: #2d3142;
$side-nav-link-active-color: #e0ddf5;
$muted-1: #8789a2;
$light-1: #9c9eb9;
$line: #cbcbd9;
$input: #171f24;
$text-muted-1: #777a91;
$text-muted-2: #52556a;
$text-muted-3: #cdcedc;
$body-color: #212529;
$warning-2: #ffefe5;
$secondary-2: #ebe9f8;
$orangeColor: #FAAF40;
$side-menu-top-pos: 58px;
$nav-width: 300px;
$nav-width-sm: 71px;

$enable-smooth-scroll: false;



$f-sb: "Cairo";
$f-r: "Cairo";
$f-bl: "Cairo";
$f-b: "Cairo";
$f-h: "Cairo";
$f-ho: "Cairo";
$f-l: "Cairo";
$fontEn : 'Cairo';
$fontAr : 'Cairo';

$breadcrumb-divider: quote(">");

//  New Theme Variables 

$radius-sm: 3px;
$radius-md: 6px;

$font-size-s: 12px;
$font-size-m: 14px;
$font-size-l: 16px;

$dark: #272728;

$gray1 : #A3A4A5;
$gray2 : #7C7C7C;

$red : #B1362F;

$orangeColorBackground: #FAAF401A;
$primaryBackground: #7222821A;
$greyBackground: #EEEEEE;