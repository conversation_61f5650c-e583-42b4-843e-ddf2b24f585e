<div class="profile_cover_box">
    <img class="profile-cover-photo" [lazyload]="user.coverURL + '?v=' + randomVersion" alt="Cover Photo"
        [placeholder]="'assets/img/placeholder/no-image-transparent.png'" />

    <!-- <label class="profile-cover-btn" [ngClass]="{'loading' : loading}">
        <input class="img_uplaod" #Image type="file" accept="image/*, .heic, .heif"
            (change)="handleFileInput($event)" />
        <i class="camera_icon">
            <svg data-id="camera_icon" viewBox="0 0 14 12" fill="none">
                <path
                    d="M11.6654 2.00131H9.9387L8.80536 0.861313C8.74307 0.799526 8.66919 0.750642 8.58797 0.717466C8.50674 0.684289 8.41977 0.667473 8.33203 0.66798H5.66536C5.57763 0.667473 5.49065 0.684289 5.40943 0.717466C5.3282 0.750642 5.25432 0.799526 5.19203 0.861313L4.0587 2.00131H2.33203C1.8016 2.00131 1.29289 2.21203 0.917818 2.5871C0.542745 2.96217 0.332031 3.47088 0.332031 4.00131V9.33465C0.332031 9.86508 0.542745 10.3738 0.917818 10.7489C1.29289 11.1239 1.8016 11.3346 2.33203 11.3346H11.6654C12.1958 11.3346 12.7045 11.1239 13.0796 10.7489C13.4546 10.3738 13.6654 9.86508 13.6654 9.33465V4.00131C13.6654 3.47088 13.4546 2.96217 13.0796 2.5871C12.7045 2.21203 12.1958 2.00131 11.6654 2.00131ZM6.9987 9.33465C6.47128 9.33465 5.95571 9.17825 5.51718 8.88523C5.07865 8.59222 4.73685 8.17574 4.53502 7.68847C4.33319 7.2012 4.28038 6.66502 4.38327 6.14774C4.48616 5.63046 4.74014 5.1553 5.11308 4.78236C5.48602 4.40942 5.96117 4.15545 6.47846 4.05255C6.99574 3.94966 7.53192 4.00247 8.01919 4.2043C8.50646 4.40613 8.92293 4.74793 9.21595 5.18646C9.50897 5.62499 9.66536 6.14056 9.66536 6.66798C9.66536 7.37522 9.38441 8.0535 8.88432 8.5536C8.38422 9.0537 7.70594 9.33465 6.9987 9.33465Z"
                    fill="currentColor" />
            </svg>

        </i>
        <span *ngIf="!user.coverURL">{{ 'ADD_COVER_PHOTO' | translate }}</span>
    </label> -->

    <label class="profile-cover-btn" [ngClass]="{'loading' : loading}">
        <app-dynamic-header>
            <ng-template #headerFilter>
                <div class="filterHeader" (click)="goToEditProfile()">
                    تعديل البيانات
                    <app-svg-icons name="edit-icon" width="15px" height="15px"></app-svg-icons>
                </div>
            </ng-template>
        </app-dynamic-header>
    </label>
</div>