<div class="profile_cover_box">
    <img class="profile-cover-photo" [lazyload]="user.coverURL + '?v=' + randomVersion" alt="Cover Photo"
        [placeholder]="'assets/img/placeholder/no-image-transparent.png'" />

    <label class="profile-cover-btn" [ngClass]="{'loading' : loading}"  *ngIf="isEditMode; else EditModetemplate">
        <app-dynamic-header>
            <ng-template #headerFilter>
                <div class="filterHeader" (click)="goToEditProfile()">
                    تعديل البيانات
                    <app-svg-icons name="edit-icon" width="12px" height="12px"></app-svg-icons>
                </div>
            </ng-template>
        </app-dynamic-header>
    </label>
    
    <ng-template #EditModetemplate>
        <label class="profile-cover-btn" [ngClass]="{'loading' : loading}">
            <input class="img_uplaod" #Image type="file" accept="image/*, .heic, .heif"
                (change)="handleFileInput($event)" />
            <i class="camera_icon">
                <app-svg-icons name="edit-icon" width="9px" height="9px"></app-svg-icons>
            </i>
            <!-- <span *ngIf="!user.coverURL">{{ 'ADD_COVER_PHOTO' | translate }}</span> -->
        </label>
    </ng-template>
</div>