<div class="container">
  <app-dynamic-header [showCloseButton]="false" [title]="'النقاط'" (backButtonClicked)="goback()"></app-dynamic-header>
  <br />
  <ng-container *ngIf="pointsData.length > 0; else noPoints">
    <app-points-status-widget [title]="'حالة نقاط الباقة'" [usedPoints]="22" [current]="100" [pointsRequired]="0"
      [label]="'شراء نقاط'" [iconName]="'icon-circle-plus'" [expiresAt]="'2025-02-11'"></app-points-status-widget>
    <br>
    <app-points-status-widget [title]="'نقاط اضافية'" [usedPoints]="22" [current]="100" [pointsRequired]="0"
      [label]="'تمديد المدة '" [expiresAt]="'2025-02-11'"></app-points-status-widget>
    <br />
    <div class="points-card" *ngFor="let item of pointsData">
      <div class="points-row">
        <div class="points-actions">
          <div class="points-text"> {{ pointsText }} </div>
          <div class="buy-btn" [ngStyle]="{ 'background-color': buyBtnBg}">
            <span [ngStyle]="{ 'color': buyBtnTextColor }">{{ buyBtnText }}</span>
          </div>
        </div>
        <div class="price">
          <span class="value"> {{ price }} </span>
          <span class="currency"> {{ currency }} </span>
        </div>
      </div>

      <div class="points-info">
        <div class="info-text"> {{ label }} </div>
      </div>
      <div class="points-info">
        <div class="info-date">
          <span class="label">{{ dateLabel }}</span>
          <span>{{ expiresAt }}</span>
        </div>
      </div>
      <div class="divider"></div>
    </div>
  </ng-container>

  <br />
  <ng-template #noPoints>
    <app-points-status-widget [title]="'حالة نقاط الباقة'" [usedPoints]="0" [current]="100" [pointsRequired]="0"
      [label]="'شراء نقاط'" [iconName]="'icon-circle-plus'" [expiresAt]="'2025-02-11'"></app-points-status-widget>
    <div class="points-empty">
      <app-svg-icons name="icon-two-circles-plus" width="31px" height="31px"></app-svg-icons>
      <p class="points-empty__text">لم تتم أي معاملات للنقاط حتى الآن</p>
    </div>
  </ng-template>
</div>