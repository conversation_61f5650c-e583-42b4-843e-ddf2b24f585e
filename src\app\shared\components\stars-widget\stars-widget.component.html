<div class="rating-container">

    <div class="prime-half-rating">
        <ng-container *ngFor="let i of [1, 2, 3, 4, 5]">
            <i *ngIf="i <= full" class="pi pi-star-fill filled"></i>

            <span *ngIf="hasHalf && i === full + 1" class="half">
                <i class="pi pi-star empty"></i>
                <i class="pi pi-star-fill filled"></i>
            </span>

            <i *ngIf="i > full + (hasHalf ? 1 : 0)" class="pi pi-star empty"></i>
        </ng-container>
    </div>

    <div class="rating-text">{{ratingValue | number:'1.1-1'}} | </div>
    <div class="rating-text">{{numberOfRating }} </div>
    <div class="rating-text">{{'ratings' | translate}}</div>
</div>