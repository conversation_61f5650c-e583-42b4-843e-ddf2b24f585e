import { ChangeDetectionStrategy, Component, ContentChild, EventEmitter, Input, Output, TemplateRef } from '@angular/core';
import { SvgIconsComponent } from '../svg-icons/svg-icons.component';
import { CommonModule, Location } from '@angular/common';
import { AlertHandlerService } from '@src/app/modules/core/alerts/alert-handler.service';
import { AdsAndAddOnsService } from '@src/app/modules/ads-and-add-ons/services/ads-and-add-ons.service';
import { ViewSwitchService } from '@src/app/modules/business-package/services/view-switch.service';

@Component({
  selector: 'app-dynamic-header',
  standalone: true,
  imports: [SvgIconsComponent, CommonModule],
  templateUrl: './dynamic-header.component.html',
  styleUrl: './dynamic-header.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class DynamicHeaderComponent {
  @Input() title: string = '';
  @Input() showBackButton: boolean = true;
  @Input() showCloseButton: boolean = true;
  @Input() showFilterButton: boolean = false;

  @Output() backButtonClicked = new EventEmitter<void>();
  @Output() filterButtonClicked = new EventEmitter<void>();

  @ContentChild('headerFilter', { read: TemplateRef }) filterSlot?: TemplateRef<any>;

  hasFilterSlot = false;

  constructor(private alertHandlerService: AlertHandlerService, private adsAndAddOnsService: AdsAndAddOnsService, private location: Location, private view: ViewSwitchService) { }

  ngAfterContentInit() {
    this.hasFilterSlot = !!this.filterSlot;
  }
  
  onBackButtonClick() {
    console.log(this.alertHandlerService.ref);

    if (this.alertHandlerService.ref) {
      this.backButtonClicked.emit();
    } else {
      this.location.back();
    }
  }

  onFilterButtonClick() {
    this.filterButtonClicked.emit();
  }

  onCloseButtonClick() {

    if (this.alertHandlerService.ref) {
      this.alertHandlerService.ref.destroy();
      this.alertHandlerService.ref = null;
      this.adsAndAddOnsService.reset();
      this.view.reset();
    }

  }

}
