import { NgFor, NgIf } from "@angular/common";
import { Component } from "@angular/core";
import { Router } from '@angular/router';

@Component({
  selector: "app-ratings",
  standalone: true,
  imports: [NgFor, NgIf],
  templateUrl: "./ratings.component.html",
  styleUrl: "./ratings.component.scss",
})
export class RatingsComponent {
  [x: string]: any;
  totalRating = 4.8;
  ratingCount = 23;
  constructor(private router: Router) { }
  ratingDistribution = [
    { stars: 5, count: 161 },
    { stars: 4, count: 31 },
    { stars: 3, count: 1 },
    { stars: 2, count: 0 },
    { stars: 1, count: 0 },
  ];

  reviews = [
    {
      user: "محمد علي",
      ad: "شقة للبيع متشطبة استلام فوري",
      time: "منذ ١٧ ساعة",
      stars: 5,
      comment:
        "التعامل ممتاز والتوصيل كان سريع، والتلفزيون وصل بحالة ممتازة. أنصح بالتعامل مع هذا البائع",
    },
    {
      user: "محمد علي",
      ad: "شقة للبيع متشطبة استلام فوري",
      time: "منذ ١٧ ساعة",
      stars: 4,
    },
  ];
  round(n: number): number {
    return Math.round(n);
  }
  goback() {
    this.router.navigate([`/authentication/new-profile/index`]);
  }
}
