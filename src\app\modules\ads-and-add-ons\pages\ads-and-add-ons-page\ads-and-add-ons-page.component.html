<div class="dashboard-page">
    <app-dynamic-header title="إدارة الإعلانات و الإضافات" (filterButtonClicked)="openFilter()"
        [showFilterButton]="true"></app-dynamic-header>

    <div *ngIf="getActiveFilters().length > 0" class="quick-buttons">

        <!-- Loop through filters dynamically -->
        <ng-container *ngFor="let filter of getActiveFilters(); let i = index">
            <button class="add-button" *ngIf="filter.key !== 'messageRange'">
                <ng-container>
                    {{ filter.label }}
                    <span class="remove-filter" (click)="removeFilter(filter.key, $event)">×</span>
                </ng-container>
            </button>
        </ng-container>


        <!-- Date range -->
        <button class="add-button" *ngIf="filtersValues.selectedDateRange?.length > 0">
            {{ 'from' | translate }} {{ filtersValues.selectedDateRange[0].split('/').slice(0, 2).join('/') }}
            <span *ngIf="filtersValues.selectedDateRange[1]">
                -
                {{ 'to' | translate }} {{ filtersValues.selectedDateRange[1].split('/').slice(0, 2).join('/') }}
            </span>
            <span class="remove-filter" (click)="removeFilter('selectedDateRange', $event)">×</span>
        </button>

        <!-- Clear All -->
        <button class="add-button delete" (click)="clearAllFilters()">
            {{ 'clearAll' | translate }}
            <app-svg-icons width="10px" height="10px" name="trash-icon"></app-svg-icons>
        </button>
    </div>

    <app-stats-block [stats]="dashboardStats"></app-stats-block>

    <div class="section-title">
        <span class="ads-stats">احصائيات الإعلانات (٣٤٢)</span>

        <span class="grouped-ads" (click)="selectedMode = !selectedMode" *ngIf="!selectedMode;else selectedModeTemp">
            إجراءات جماعية
        </span>

        <ng-template #selectedModeTemp>
            <div class="action-buttons">
                <button class="add-button" (click)="addSelectedAds()">
                    <app-svg-icons name="icon-circle-plus" width="10px" height="10px"></app-svg-icons>
                    <span>الاضافة</span>
                </button>

                <button class="cancel-button" (click)="cancel()">
                    {{'Cancel' | translate}}
                </button>
            </div>
        </ng-template>

    </div>

    <div class="ads-list">
        <div *ngFor="let ad of ads" (click)="toggleSelection(ad.id)">
            <app-listing-card-row [listingDetails]="ad" [isSelectedMode]="selectedMode"
                [isSelected]="isSelected(ad.id)"></app-listing-card-row>
        </div>
    </div>
</div>