<div class="ps-card" [ngClass]="{'clickedMode': clickedMode}" [class.over]="cannotBuy">
    <div class="ps-header">
        {{title}}
        <app-svg-icons *ngIf="clickedMode" name="icon-arrow-left" width="15px" height="15px"
            color="rgba(250, 175, 64, 1)"></app-svg-icons>
    </div>

    <div class="ps-row">
        <div class="ps-bar-wrap">
            <div class="ps-bar-bg" [ngClass]="{'clickedMode': clickedMode}"></div>
            <div class="ps-bar-will-fill" [style.width.%]="percentwithRequired" [class.over]="cannotBuy"
                *ngIf="pointsRequired">
            </div>
            <div class="ps-bar-fill" [style.width.%]="percent" [class.over]="cannotBuy"></div>
        </div>

        <div class="ps-count" [class.over]="cannotBuy">
            <span dir="ltr">
                <span *ngIf="cannotBuy">-</span> {{ pointsRequired ? usedPoints + pointsRequired : usedPoints }}/{{
                current }}
            </span>
        </div>
    </div>

    <div class="ps-details" *ngIf="showDetials">
        <div><strong>لديك:</strong> {{ current }} نقطة</div>
        <div><strong>المستهلك:</strong> {{ usedPoints }} نقطة</div>
        <div><strong>هذة الاضافة تحتاج:</strong> {{ pointsRequired }} نقطة</div>
        <div><strong> تنتهي في:</strong> 11 فبراير 2025</div>
    </div>

    <div class="ps-footer" *ngIf="expiresAt">
        <span class="note-icon">
            <app-svg-icons name="icon-info" width="12" height="12"></app-svg-icons>
        </span>
        <span class="ps-expiry"> تنتهي في 11 فبراير 2025</span>
        <!-- <span class="ps-expiry">{{ expiresAt | date:'longDate' }}</span> -->
    </div>
    <app-shared-btn *ngIf="label" [label]="label" [iconName]="iconName" [iconWidth]="'10px'" [iconHeight]="'10px'"
        [size]="'small'" [theme]="'bgWhite'"></app-shared-btn>
</div>