import { <PERSON>a, StoryObj, moduleMetadata } from "@storybook/angular";
import { CancelConfirmationComponent } from "./cancel-confirmation.component";
import { SharedBtnComponent } from "@src/app/shared/components/shared-btn/shared-btn.component";
import { SvgIconsComponent } from "@src/app/shared/components/svg-icons/svg-icons.component";
import { DynamicHeaderComponent } from "@src/app/shared/components/dynamic-header/dynamic-header.component";
import { AlertHandlerService } from "@src/app/modules/core/alerts/alert-handler.service";
import { DynamicDialogConfig, DynamicDialogRef } from "primeng/dynamicdialog";
import { ViewSwitchService } from "../../services/view-switch.service";
import { PackagesService } from "../../services/packages.service";

// (اختياري) لو LongDatePipe بيعتمد على خدمات ترجمة/لغة:
// import { TranslateService } from "@ngx-translate/core";
// const mockTranslate = { instant: (k: string) => k };
// const mockLang = { lang: "ar-eg" };

const meta: Meta<CancelConfirmationComponent> = {
  title: "BusinessPackages/Shared/CancelConfirmation",
  component: CancelConfirmationComponent,
  tags: ["autodocs"],
  decorators: [
    moduleMetadata({
      imports: [SharedBtnComponent, SvgIconsComponent, DynamicHeaderComponent],
      providers: [
        { provide: AlertHandlerService, useValue: {} },

        { provide: DynamicDialogRef, useValue: { close: () => {} } },
        { provide: DynamicDialogConfig, useValue: { data: null } },

        { provide: ViewSwitchService, useValue: { go: (_: string) => {} } },
        { provide: PackagesService, useValue: { selectedPlan: () => ({ packageEndDate:"2025-09-20T10:32:19.377Z" }) } },
      ],
    }),
  ],
  render: (args) => ({ props: args }),
};

export default meta;
type Story = StoryObj<CancelConfirmationComponent>;

export const Default: Story = {
  args: {
    title: "هل أنت متأكد من رغبتك في إلغاء الباقة؟",
    description: "ستتم إلغاء الباقة في",
    cancelDateValue: "2024-01-28T00:00:00Z",
    btnText: "إلغاء تجديد الباقة",
  },
};

export const ChangeTitle: Story = {
  args: {
    title: "هل أنت متأكد من رغبتك في تحويل الي حساب فرد؟",
    description: "عند تحويل نوع الحساب ، سيتم إزالة أي إضافة او اعلانات تم شراؤها مسبقًا",
    cancelDateValue: null,
    btnText: "تحويل الي حساب فرد",
  },
};
