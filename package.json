{"name": "swapp-frontend", "version": "1.0.0", "scripts": {"ng": "ng", "start": "ng serve", "start:staging": "ng serve --configuration staging", "build": "ng build", "watch": "ng build --watch --configuration development", "test": "ng test", "purgecss": "node ./purgecss.js", "postbuild": "node postbuild-rollup.js", "build:custom": "ng build && ng run swapp-frontend:server", "build:prerender": "ng build && ng run swapp-frontend:prerender", "serve:ssr:swapp-frontend": "node dist/swapp-frontend/server/server.mjs", "build:ssr:prod": "ng build --configuration=production", "build:ssr:local": "ng build --configuration=development", "build:ssr:staging": "ng build --configuration=staging", "build:ssr:beta": "ng build --configuration=beta", "custom-moment": "npm install git+ssh://github.com/smartcaeser/swappmoment", "build:source-map": "ng build --configuration production --source-map && source-map-explorer dist/swapp-frontend/server/*.mjs && source-map-explorer dist/swapp-frontend/browser/*.mjs", "start:ssr:local": "set PORT=4200 && node dist/swapp-frontend/server/server.mjs", "storybook": "ng run swapp-frontend:storybook", "build-storybook": "ng run swapp-frontend:build-storybook", "docker:storybook:build": "docker build -f Dockerfile.storybook -t swapp-storybook .", "docker:storybook:run": "docker run -p 8333:6006 --name swapp-storybook-local swapp-storybook", "docker:storybook:stop": "docker stop swapp-storybook-local && docker rm swapp-storybook-local"}, "private": true, "dependencies": {"@amplitude/analytics-browser": "^2.18.0", "@angular-builders/custom-esbuild": "^18.0.0", "@angular-builders/custom-webpack": "^18.0.0", "@angular/animations": "^18.2.5", "@angular/cdk": "^18.2.14", "@angular/common": "^18.2.5", "@angular/compiler": "^18.2.5", "@angular/core": "^18.2.5", "@angular/forms": "^18.2.5", "@angular/platform-browser": "^18.2.5", "@angular/platform-browser-dynamic": "^18.2.5", "@angular/platform-server": "^18.2.5", "@angular/router": "^18.2.5", "@angular/ssr": "^18.2.5", "@keyv/redis": "^4.3.1", "@ngneat/until-destroy": "^10.0.0", "@ngrx/effects": "^17.2.0", "@ngrx/store": "^17.2.0", "@ngrx/store-devtools": "^18.1.0", "@nguniversal/express-engine": "^16.2.0", "@ngx-translate/core": "^15.0.0", "@ngx-translate/http-loader": "^8.0.0", "@rx-angular/isr": "^18.1.0", "angular-auth-oidc-client": "18.0.1", "angular-google-tag-manager": "^1.10.0", "angular-oauth2-oidc": "^17.0.2", "angular-oauth2-oidc-jwks": "^17.0.2", "angular-star-rating": "^7.0.0", "apexcharts": "^5.3.3", "axios": "^1.7.9", "cache-manager": "^6.4.1", "cacheable": "^1.8.9", "child_process": "^1.0.2", "compression": "^1.7.4", "cors": "^2.8.5", "express": "^4.18.2", "fs": "^0.0.1-security", "hammerjs": "^2.0.8", "heic-to": "^1.0.2", "i": "^0.3.7", "keyv": "^5.3.2", "libheif-js": "^1.18.2", "moment": "^2.30.1", "ng-apexcharts": "^2.0.0", "ngx-cookie-service-ssr": "^18.0.0", "ngx-infinite-scroll": "^17.0.0", "ngx-otp-input": "^1.1.1", "ngx-progressbar": "^14.0.0", "npm": "^10.9.0", "path": "^0.12.7", "primeicons": "^7.0.0", "primeng": "^17.18.11", "rxjs": "~7.8.0", "swappmoment": "github:smartcaeser/swappmoment", "tslib": "^2.3.0", "xmlbuilder": "^15.1.1", "zone.js": "~0.14.3"}, "devDependencies": {"@angular-devkit/architect": "^0.1802.20", "@angular-devkit/build-angular": "^18.2.20", "@angular-devkit/core": "^18.2.20", "@angular-devkit/schematics": "^18.2.20", "@angular/cli": "^18.2.5", "@angular/compiler-cli": "^18.2.5", "@compodoc/compodoc": "^1.1.26", "@schematics/angular": "^18.2.20", "@storybook/addon-docs": "^9.0.16", "@storybook/angular": "^9.0.16", "@types/express": "^4.17.17", "@types/jasmine": "~5.1.0", "@types/node": "^18.18.0", "esbuild": "^0.24.0", "jasmine-core": "~5.1.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "moment-locales-webpack-plugin": "^1.2.0", "moment-timezone-data-webpack-plugin": "^1.5.1", "purgecss": "^7.0.2", "rimraf": "^6.0.1", "storybook": "^9.0.16", "ts-loader": "^9.5.1", "typescript": "~5.4.2"}}